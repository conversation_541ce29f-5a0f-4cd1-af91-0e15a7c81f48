#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
网络问题修复脚本
用于诊断和修复移动端应用的网络连接问题
"""

import os
import sys
import logging
import json
import time

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def clear_proxy_settings():
    """清除所有代理设置"""
    print("正在清除代理设置...")

    proxy_vars = [
        'HTTP_PROXY', 'HTTPS_PROXY', 'FTP_PROXY',
        'http_proxy', 'https_proxy', 'ftp_proxy',
        'ALL_PROXY', 'all_proxy',
        'SOCKS_PROXY', 'socks_proxy',
        'SOCKS4_PROXY', 'socks4_proxy',
        'SOCKS5_PROXY', 'socks5_proxy'
    ]

    removed_count = 0
    for var in proxy_vars:
        if var in os.environ:
            old_value = os.environ.pop(var)
            print(f"  已移除: {var} = {old_value}")
            removed_count += 1

    # 设置NO_PROXY
    os.environ['NO_PROXY'] = '*'
    os.environ['no_proxy'] = '*'

    print(f"已移除 {removed_count} 个代理环境变量")
    return removed_count > 0

def test_network_connectivity():
    """测试网络连接"""
    print("正在测试网络连接...")

    test_urls = [
        'http://************:80/api/health',
        'http://localhost:8006/api/health',
        'https://www.baidu.com'
    ]

    results = {}

    try:
        import requests

        for url in test_urls:
            try:
                print(f"  测试: {url}")
                start_time = time.time()

                # 构建请求参数，兼容不同版本的requests
                request_kwargs = {
                    'timeout': 10,
                    'proxies': {
                        'http': None,
                        'https': None,
                        'ftp': None,
                        'socks4': None,
                        'socks5': None
                    },
                    'verify': False
                }

                # 检查是否支持trust_env参数
                try:
                    import inspect
                    sig = inspect.signature(requests.get)
                    if 'trust_env' in sig.parameters:
                        request_kwargs['trust_env'] = False
                except Exception:
                    # 如果检查失败，不添加trust_env参数
                    pass

                response = requests.get(url, **request_kwargs)
                end_time = time.time()
                response_time = round(end_time - start_time, 3)

                results[url] = {
                    'status': 'success',
                    'status_code': response.status_code,
                    'response_time': response_time
                }

                print(f"    ✓ 成功 (状态码: {response.status_code}, 响应时间: {response_time}s)")

            except Exception as e:
                results[url] = {
                    'status': 'failed',
                    'error': str(e)
                }
                print(f"    ✗ 失败: {str(e)}")

    except ImportError:
        print("  错误: requests库未安装")
        return {}

    return results

def check_dns_resolution():
    """检查DNS解析"""
    print("正在检查DNS解析...")

    test_domains = [
        '************',
        'localhost',
        'www.baidu.com'
    ]

    results = {}

    try:
        import socket

        for domain in test_domains:
            try:
                print(f"  解析: {domain}")
                start_time = time.time()
                ip = socket.gethostbyname(domain)
                end_time = time.time()
                resolution_time = round(end_time - start_time, 3)

                results[domain] = {
                    'status': 'success',
                    'ip': ip,
                    'resolution_time': resolution_time
                }

                print(f"    ✓ 成功: {domain} -> {ip} ({resolution_time}s)")

            except Exception as e:
                results[domain] = {
                    'status': 'failed',
                    'error': str(e)
                }
                print(f"    ✗ 失败: {str(e)}")

    except ImportError:
        print("  错误: socket库不可用")
        return {}

    return results

def flush_dns_cache():
    """清除DNS缓存（Windows）"""
    print("正在清除DNS缓存...")

    try:
        import subprocess
        import platform

        if platform.system().lower() == 'windows':
            result = subprocess.run(
                ['ipconfig', '/flushdns'],
                capture_output=True,
                text=True,
                check=True
            )
            print("  ✓ DNS缓存已清除")
            return True
        else:
            print("  跳过: 非Windows系统")
            return True

    except Exception as e:
        print(f"  ✗ 清除DNS缓存失败: {str(e)}")
        return False

def generate_report(diagnosis_results):
    """生成诊断报告"""
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    report_file = f"network_diagnosis_{timestamp}.json"

    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(diagnosis_results, f, ensure_ascii=False, indent=2)

        print(f"诊断报告已保存到: {report_file}")
        return report_file

    except Exception as e:
        print(f"保存诊断报告失败: {str(e)}")
        return None

def main():
    """主函数"""
    print("=" * 60)
    print("移动端网络问题诊断和修复工具")
    print("=" * 60)

    diagnosis_results = {
        'timestamp': time.time(),
        'proxy_cleared': False,
        'network_tests': {},
        'dns_tests': {},
        'dns_cache_flushed': False,
        'recommendations': []
    }

    # 1. 清除代理设置
    diagnosis_results['proxy_cleared'] = clear_proxy_settings()

    # 2. 测试网络连接
    diagnosis_results['network_tests'] = test_network_connectivity()

    # 3. 检查DNS解析
    diagnosis_results['dns_tests'] = check_dns_resolution()

    # 4. 清除DNS缓存
    diagnosis_results['dns_cache_flushed'] = flush_dns_cache()

    # 5. 生成建议
    recommendations = []

    if diagnosis_results['proxy_cleared']:
        recommendations.append("已清除代理设置，建议重启应用")

    failed_network_tests = [
        url for url, result in diagnosis_results['network_tests'].items()
        if result.get('status') == 'failed'
    ]

    if failed_network_tests:
        recommendations.append(f"网络连接失败: {', '.join(failed_network_tests)}")
        recommendations.append("建议检查防火墙设置和网络连接")

    failed_dns_tests = [
        domain for domain, result in diagnosis_results['dns_tests'].items()
        if result.get('status') == 'failed'
    ]

    if failed_dns_tests:
        recommendations.append(f"DNS解析失败: {', '.join(failed_dns_tests)}")
        recommendations.append("建议更换DNS服务器或检查网络设置")

    if not failed_network_tests and not failed_dns_tests:
        recommendations.append("网络连接正常，如果仍有问题请重启应用")

    diagnosis_results['recommendations'] = recommendations

    # 6. 显示结果
    print("\n" + "=" * 60)
    print("诊断结果:")
    print("=" * 60)

    for recommendation in recommendations:
        print(f"• {recommendation}")

    # 7. 生成报告
    print("\n" + "=" * 60)
    generate_report(diagnosis_results)

    print("\n修复完成！请重启移动端应用以使更改生效。")
    print("如果问题仍然存在，请将诊断报告发送给技术支持。")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n用户中断操作")
    except Exception as e:
        print(f"\n\n发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

    input("\n按回车键退出...")
