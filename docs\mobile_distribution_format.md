# 移动端分发接收格式规范

## 概述
本文档定义了健康评估应用中量表和问卷分发到移动端的数据格式规范，确保云端和移动端之间的数据传输一致性和兼容性。

## 1. 分发通知格式

### 1.1 基础通知结构
```json
{
  "id": "notification_id",
  "type": "distribution",
  "content_type": "assessment|questionnaire",
  "title": "分发通知标题",
  "message": "分发通知内容",
  "distribution_data": {
    // 具体分发数据
  },
  "created_at": "2025-06-01T15:00:00Z",
  "expires_at": "2025-06-08T15:00:00Z",
  "priority": "high|medium|low",
  "read_status": false
}
```

### 1.2 量表分发格式
```json
{
  "id": "dist_assessment_001",
  "type": "distribution",
  "content_type": "assessment",
  "title": "心理健康评估量表分发",
  "message": "您有一个新的心理健康评估量表需要完成",
  "distribution_data": {
    "distribution_id": 123,
    "template_id": 1,
    "template_key": "hamilton_depression",
    "assessment_info": {
      "name": "汉密尔顿抑郁量表",
      "name_en": "Hamilton Depression Rating Scale",
      "description": "用于评估抑郁症状严重程度的专业量表",
      "category": "心理健康",
      "assessment_type": "depression",
      "sub_type": "clinical",
      "version": "1.0",
      "max_score": 52,
      "estimated_time": 15
    },
    "questions": [
      {
        "id": 1,
        "text": "抑郁情绪（悲伤、绝望、无助、无价值感）",
        "type": "single_choice",
        "options": [
          {"value": 0, "text": "无"},
          {"value": 1, "text": "轻度"},
          {"value": 2, "text": "中度"},
          {"value": 3, "text": "重度"},
          {"value": 4, "text": "极重度"}
        ],
        "required": true
      }
    ],
    "instructions": "请根据您最近一周的感受选择最符合的选项",
    "scoring_method": "sum",
    "result_ranges": [
      {"min": 0, "max": 7, "level": "正常", "description": "无抑郁症状"},
      {"min": 8, "max": 16, "level": "轻度", "description": "轻度抑郁"},
      {"min": 17, "max": 23, "level": "中度", "description": "中度抑郁"},
      {"min": 24, "max": 52, "level": "重度", "description": "重度抑郁"}
    ],
    "due_date": "2025-06-08T23:59:59Z",
    "distributor": {
      "id": 1,
      "name": "张医生",
      "role": "doctor"
    }
  },
  "created_at": "2025-06-01T15:00:00Z",
  "expires_at": "2025-06-08T23:59:59Z",
  "priority": "high",
  "read_status": false
}
```

### 1.3 问卷分发格式
```json
{
  "id": "dist_questionnaire_001",
  "type": "distribution",
  "content_type": "questionnaire",
  "title": "健康生活方式调查问卷分发",
  "message": "请完成健康生活方式调查问卷",
  "distribution_data": {
    "distribution_id": 456,
    "questionnaire_id": 2,
    "questionnaire_info": {
      "title": "健康生活方式调查",
      "description": "了解您的日常生活习惯和健康状况",
      "category": "生活方式",
      "questionnaire_type": "survey",
      "assessment_type": "self",
      "estimated_time": 10
    },
    "questions": [
      {
        "id": 1,
        "text": "您每天的睡眠时间是多少？",
        "type": "single_choice",
        "options": [
          {"value": "less_than_6", "text": "少于6小时"},
          {"value": "6_to_8", "text": "6-8小时"},
          {"value": "more_than_8", "text": "超过8小时"}
        ],
        "required": true
      },
      {
        "id": 2,
        "text": "请描述您的运动习惯",
        "type": "text",
        "required": false,
        "max_length": 500
      }
    ],
    "instructions": "请根据您的实际情况如实填写",
    "due_date": "2025-06-08T23:59:59Z",
    "distributor": {
      "id": 1,
      "name": "健康管理员",
      "role": "health_manager"
    }
  },
  "created_at": "2025-06-01T15:00:00Z",
  "expires_at": "2025-06-08T23:59:59Z",
  "priority": "medium",
  "read_status": false
}
```

## 2. 精准分发类型

### 2.1 分发类型枚举
- `all_users`: 全部用户分发
- `role_based`: 按角色分发
- `specific_users`: 指定用户分发
- `department_based`: 按部门分发
- `custom_filter`: 自定义筛选条件分发

### 2.2 角色分发示例
```json
{
  "distribution_type": "role_based",
  "target_roles": ["doctor", "nurse"],
  "exclude_roles": ["admin"],
  "filter_conditions": {
    "department": "心理科",
    "active_status": true
  }
}
```

### 2.3 指定用户分发示例
```json
{
  "distribution_type": "specific_users",
  "target_users": [
    {"user_id": 123, "custom_id": "SM_001"},
    {"user_id": 124, "custom_id": "SM_002"}
  ],
  "personalized_message": {
    "123": "张先生，请完成您的心理健康评估",
    "124": "李女士，请完成您的心理健康评估"
  }
}
```

## 3. 移动端响应格式

### 3.1 接收确认响应
```json
{
  "status": "received",
  "distribution_id": 123,
  "user_id": 456,
  "received_at": "2025-06-01T15:05:00Z",
  "device_info": {
    "platform": "iOS|Android",
    "version": "1.2.0",
    "device_id": "unique_device_identifier"
  }
}
```

### 3.2 完成提交响应
```json
{
  "status": "completed",
  "distribution_id": 123,
  "user_id": 456,
  "responses": [
    {
      "question_id": 1,
      "answer": "2",
      "answer_text": "中度"
    },
    {
      "question_id": 2,
      "answer": "我每天坚持运动30分钟"
    }
  ],
  "total_score": 15,
  "completion_time": 12,
  "completed_at": "2025-06-01T15:20:00Z",
  "device_info": {
    "platform": "iOS",
    "version": "1.2.0",
    "device_id": "unique_device_identifier"
  }
}
```

## 4. 数据同步机制

### 4.1 推送通知
- 使用Firebase Cloud Messaging (FCM) 或 Apple Push Notification Service (APNs)
- 支持静默推送和显示推送
- 包含基础分发信息，详细数据通过API获取

### 4.2 轮询机制
- 移动端定期检查新的分发任务
- 建议轮询间隔：前台5分钟，后台30分钟
- 支持增量同步，只获取更新的数据

### 4.3 离线支持
- 分发数据本地缓存
- 支持离线完成评估/问卷
- 网络恢复后自动同步结果

## 5. 安全考虑

### 5.1 数据加密
- 传输过程使用HTTPS/TLS加密
- 敏感数据字段额外加密
- 本地存储使用设备密钥加密

### 5.2 身份验证
- 每次API调用需要有效的JWT令牌
- 支持令牌刷新机制
- 设备绑定验证

### 5.3 权限控制
- 用户只能接收针对自己的分发
- 角色权限验证
- 数据访问日志记录

## 6. 错误处理

### 6.1 常见错误码
- `4001`: 分发已过期
- `4002`: 用户无权限访问
- `4003`: 分发不存在
- `4004`: 数据格式错误
- `5001`: 服务器内部错误

### 6.2 错误响应格式
```json
{
  "error": {
    "code": 4001,
    "message": "分发已过期",
    "details": "该分发任务已于2025-06-01过期",
    "timestamp": "2025-06-01T15:30:00Z"
  }
}
```

## 7. 移动端结果上传格式

### 7.1 评估量表结果上传
```json
{
  "assessment_id": 123,
  "distribution_id": 456,
  "user_id": "SM_006",
  "answers": [
    {
      "question_id": "q1",
      "question_text": "抑郁情绪（悲伤、绝望、无助、无价值感）",
      "answer_value": 2,
      "answer_text": "中度",
      "score": 2
    },
    {
      "question_id": "q2",
      "question_text": "感到悲伤和沮丧",
      "answer_value": 1,
      "answer_text": "轻度",
      "score": 1
    }
  ],
  "total_score": 15,
  "max_score": 52,
  "completion_time_minutes": 12,
  "started_at": "2025-06-02T07:45:00Z",
  "completed_at": "2025-06-02T07:57:00Z",
  "device_info": {
    "platform": "Android",
    "app_version": "1.2.0",
    "device_id": "unique_device_identifier",
    "os_version": "Android 12"
  },
  "location_info": {
    "latitude": 39.9042,
    "longitude": 116.4074,
    "accuracy": 10.0,
    "timestamp": "2025-06-02T07:57:00Z"
  }
}
```

### 7.2 问卷结果上传
```json
{
  "questionnaire_id": 789,
  "distribution_id": 101112,
  "user_id": "SM_006",
  "answers": [
    {
      "question_id": "q1",
      "question_text": "您每天的睡眠时间是多少？",
      "answer_value": "6_to_8",
      "answer_text": "6-8小时"
    },
    {
      "question_id": "q2",
      "question_text": "请描述您的运动习惯",
      "answer_value": "我每天坚持运动30分钟，主要是慢跑和瑜伽",
      "answer_text": "我每天坚持运动30分钟，主要是慢跑和瑜伽"
    }
  ],
  "completion_time_minutes": 8,
  "started_at": "2025-06-02T08:00:00Z",
  "completed_at": "2025-06-02T08:08:00Z",
  "device_info": {
    "platform": "iOS",
    "app_version": "1.2.0",
    "device_id": "unique_device_identifier",
    "os_version": "iOS 16.5"
  }
}
```

## 8. 云端处理机制

### 8.1 结果接收处理
```json
{
  "status": "success",
  "message": "评估量表提交成功",
  "data": {
    "assessment_id": 123,
    "total_score": 15,
    "completed_at": "2025-06-02T08:10:00Z",
    "answers_count": 10,
    "analysis": {
      "score_level": "中度",
      "risk_assessment": "建议咨询专业医生",
      "recommendations": [
        "保持规律作息",
        "适当运动",
        "寻求专业帮助"
      ]
    },
    "next_actions": [
      {
        "type": "follow_up",
        "description": "2周后复评",
        "due_date": "2025-06-16T00:00:00Z"
      }
    ]
  }
}
```

### 8.2 数据验证规则
- **路径参数验证**: assessment_id/questionnaire_id 必须为有效的数字ID
- **请求头验证**: 必须包含有效的Authorization和X-User-ID
- **必填字段验证**: answers数组不能为空
- **答案完整性**: 所有必答题目必须有答案
- **数据类型验证**: question_id必须为数字，score必须为数字（如果提供）
- **权限验证**: 用户只能提交分配给自己的评估/问卷
- **重复提交检查**: 防止同一评估/问卷多次提交
- **状态验证**: 只能提交状态为'pending'的评估/问卷

### 8.3 异常处理
```json
{
  "status": "error",
  "error_code": "VALIDATION_FAILED",
  "message": "数据验证失败",
  "details": {
    "missing_fields": ["question_q3_answer"],
    "invalid_fields": {
      "total_score": "分数不能为负数"
    }
  },
  "timestamp": "2025-06-02T08:10:00Z"
}
```

### 8.4 数据存储策略
- **主数据表**: 存储评估/问卷基本信息和总分
- **详细答案表**: 存储每个问题的具体答案
- **分析结果表**: 存储AI分析和专业建议
- **审计日志**: 记录所有操作和状态变更

## 9. 移动端API端点

### 9.1 获取分发列表
```
GET /api/mobile/assessments
GET /api/mobile/questionnaires
```

**请求头**:
```
Authorization: Bearer {jwt_token}
X-User-ID: {custom_id}
Content-Type: application/json
```

**响应格式**:
```json
{
  "status": "success",
  "data": [
    {
      "id": 123,
      "distribution_id": 456,
      "name": "汉密尔顿抑郁量表",
      "assessment_type": "depression",
      "status": "pending",
      "total_score": null,
      "due_date": "2025-06-08T23:59:59Z",
      "created_at": "2025-06-01T10:00:00Z",
      "completed_at": null,
      "template": {
        "id": 1,
        "name": "汉密尔顿抑郁量表",
        "assessment_type": "depression",
        "version": "1.0",
        "description": "用于评估抑郁症状严重程度",
        "instructions": "请根据最近一周的感受回答以下问题",
        "questions": [
          {
            "id": 1,
            "question_id": "q1",
            "question_text": "您的心情如何？",
            "question_type": "single_choice",
            "options": ["很好", "一般", "较差", "很差"],
            "order": 1,
            "is_required": true,
            "jump_logic": null
          }
        ]
      }
    }
  ]
}
```

### 9.2 提交结果
```
POST /api/mobile/assessments/{assessment_id}/submit
POST /api/mobile/questionnaires/{questionnaire_id}/submit
```

**请求头**:
```
Authorization: Bearer {jwt_token}
X-User-ID: {custom_id}
Content-Type: application/json
```

**请求体格式**:
```json
{
  "answers": [
    {
      "question_id": 1,
      "answer": "2",
      "score": 2
    },
    {
      "question_id": 2,
      "answer": "我每天坚持运动30分钟"
    }
  ]
}
```

**响应格式**:
```json
{
  "status": "success",
  "message": "评估量表提交成功",
  "data": {
    "assessment_id": 123,
    "total_score": 15,
    "completed_at": "2025-06-02T08:10:00Z",
    "answers_count": 10
  }
}
```

## 10. 故障排查指南

### 10.1 常见问题

**问题1**: 移动端接收不到分发的量表/问卷
- **可能原因**: 
  - 分发记录未正确创建
  - 用户ID不匹配
  - API端点URL错误
- **排查步骤**:
  1. 检查数据库中的`assessment_distributions`或`questionnaire_distributions`表
  2. 验证用户的`custom_id`是否正确
  3. 确认API URL格式正确（注意不要有多余的逗号）

**问题2**: 结果上传失败
- **可能原因**:
  - 数据格式不正确
  - 权限验证失败
  - 网络连接问题
- **排查步骤**:
  1. 检查请求数据格式是否符合规范
  2. 验证用户是否有权限提交该评估/问卷
  3. 检查网络连接和服务器状态

### 10.2 调试工具
- **后端日志**: 查看`/www/wwwroot/healthapp/backend.log`
- **数据库查询**: 直接查询分发表确认数据
- **API测试**: 使用Postman或curl测试API端点

## 11. 版本兼容性

### 11.1 API版本控制
- 使用语义化版本号 (v1.0.0)
- 向后兼容原则
- 废弃功能提前通知

### 11.2 数据格式演进
- 新增字段向后兼容
- 必要时提供数据转换接口
- 版本迁移指南

---

**文档版本**: 1.1.0  
**最后更新**: 2025-06-02  
**维护者**: 健康评估应用开发团队