# 分发通知管理器
import os
import json
import logging
from datetime import datetime, timezone
from typing import List, Dict, Optional

logger = logging.getLogger(__name__)

class DistributionManager:
    """分发通知管理器，处理评估量表和问卷的分发通知"""
    
    def __init__(self):
        """初始化分发管理器"""
        self.base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.data_dir = os.path.join(self.base_dir, 'data')
        self.distributions_dir = os.path.join(self.data_dir, 'distributions')
        
        # 确保目录存在
        os.makedirs(self.distributions_dir, exist_ok=True)
        
        # 分发数据文件
        self.distributions_file = os.path.join(self.data_dir, 'distributions.json')
        
        # 初始化分发数据
        self._init_distributions()
    
    def _init_distributions(self):
        """初始化分发数据文件"""
        if not os.path.exists(self.distributions_file):
            initial_data = {
                "assessments": [],
                "questionnaires": [],
                "last_sync": None
            }
            self._save_distributions(initial_data)
    
    def _load_distributions(self) -> Dict:
        """加载分发数据"""
        try:
            if os.path.exists(self.distributions_file):
                with open(self.distributions_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {"assessments": [], "questionnaires": [], "last_sync": None}
        except Exception as e:
            logger.error(f"加载分发数据失败: {str(e)}")
            return {"assessments": [], "questionnaires": [], "last_sync": None}
    
    def _save_distributions(self, data: Dict):
        """保存分发数据"""
        try:
            with open(self.distributions_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存分发数据失败: {str(e)}")
    
    def process_distribution_notification(self, notification: Dict) -> bool:
        """处理分发通知
        
        Args:
            notification: 分发通知数据，符合mobile_distribution_format.md格式
            
        Returns:
            bool: 处理是否成功
        """
        try:
            # 验证通知格式
            if not self._validate_notification(notification):
                logger.error("分发通知格式无效")
                return False
            
            content_type = notification.get('content_type')
            distribution_data = notification.get('distribution_data', {})
            
            # 加载现有分发数据
            data = self._load_distributions()
            
            if content_type == 'assessment':
                # 处理评估量表分发
                self._process_assessment_distribution(data, distribution_data)
            elif content_type == 'questionnaire':
                # 处理问卷分发
                self._process_questionnaire_distribution(data, distribution_data)
            else:
                logger.error(f"未知的分发内容类型: {content_type}")
                return False
            
            # 更新同步时间
            data['last_sync'] = datetime.now().isoformat()
            
            # 保存更新后的数据
            self._save_distributions(data)
            
            logger.info(f"成功处理{content_type}分发通知")
            return True
            
        except Exception as e:
            logger.error(f"处理分发通知失败: {str(e)}")
            return False
    
    def _validate_notification(self, notification: Dict) -> bool:
        """验证分发通知格式"""
        required_fields = ['id', 'type', 'content_type', 'distribution_data']
        return all(field in notification for field in required_fields)
    
    def _process_assessment_distribution(self, data: Dict, distribution_data: Dict):
        """处理评估量表分发"""
        assessment_info = distribution_data.get('assessment_info', {})
        questions = distribution_data.get('questions', [])
        
        # 构建评估量表数据
        assessment = {
            'id': distribution_data.get('distribution_id'),
            'template_id': distribution_data.get('template_id'),
            'template_key': distribution_data.get('template_key'),
            'name': assessment_info.get('name'),
            'name_en': assessment_info.get('name_en'),
            'description': assessment_info.get('description'),
            'category': assessment_info.get('category'),
            'assessment_type': assessment_info.get('assessment_type'),
            'sub_type': assessment_info.get('sub_type'),
            'version': assessment_info.get('version'),
            'max_score': assessment_info.get('max_score'),
            'estimated_time': assessment_info.get('estimated_time'),
            'questions': self._process_questions(questions),
            'instructions': distribution_data.get('instructions'),
            'scoring_method': distribution_data.get('scoring_method'),
            'result_ranges': distribution_data.get('result_ranges', []),
            'due_date': distribution_data.get('due_date'),
            'distributor': distribution_data.get('distributor', {}),
            'status': 'pending',
            'received_at': datetime.now().isoformat()
        }
        
        # 检查是否已存在，如果存在则更新，否则添加
        existing_index = self._find_assessment_index(data['assessments'], assessment['id'])
        if existing_index >= 0:
            data['assessments'][existing_index] = assessment
        else:
            data['assessments'].append(assessment)
    
    def _process_questionnaire_distribution(self, data: Dict, distribution_data: Dict):
        """处理问卷分发"""
        questionnaire_info = distribution_data.get('questionnaire_info', {})
        questions = distribution_data.get('questions', [])
        
        # 构建问卷数据
        questionnaire = {
            'id': distribution_data.get('distribution_id'),
            'questionnaire_id': distribution_data.get('questionnaire_id'),
            'title': questionnaire_info.get('title'),
            'description': questionnaire_info.get('description'),
            'category': questionnaire_info.get('category'),
            'questionnaire_type': questionnaire_info.get('questionnaire_type'),
            'assessment_type': questionnaire_info.get('assessment_type'),
            'estimated_time': questionnaire_info.get('estimated_time'),
            'questions': self._process_questions(questions),
            'instructions': distribution_data.get('instructions'),
            'due_date': distribution_data.get('due_date'),
            'distributor': distribution_data.get('distributor', {}),
            'status': 'pending',
            'received_at': datetime.now().isoformat()
        }
        
        # 检查是否已存在，如果存在则更新，否则添加
        existing_index = self._find_questionnaire_index(data['questionnaires'], questionnaire['id'])
        if existing_index >= 0:
            data['questionnaires'][existing_index] = questionnaire
        else:
            data['questionnaires'].append(questionnaire)
    
    def _process_questions(self, questions: List[Dict]) -> List[Dict]:
        """处理题目数据，确保格式统一"""
        processed_questions = []
        
        for question in questions:
            processed_question = {
                'id': question.get('id'),
                'question_id': question.get('id'),  # 兼容字段
                'text': question.get('text'),
                'question_text': question.get('text'),  # 兼容字段
                'type': question.get('type'),
                'question_type': question.get('type'),  # 兼容字段
                'options': question.get('options', []),
                'required': question.get('required', True),
                'max_length': question.get('max_length')
            }
            processed_questions.append(processed_question)
        
        return processed_questions
    
    def _find_assessment_index(self, assessments: List[Dict], assessment_id) -> int:
        """查找评估量表在列表中的索引"""
        for i, assessment in enumerate(assessments):
            if assessment.get('id') == assessment_id:
                return i
        return -1
    
    def _find_questionnaire_index(self, questionnaires: List[Dict], questionnaire_id) -> int:
        """查找问卷在列表中的索引"""
        for i, questionnaire in enumerate(questionnaires):
            if questionnaire.get('id') == questionnaire_id:
                return i
        return -1
    
    def get_pending_assessments(self, user_id: Optional[str] = None) -> List[Dict]:
        """获取待完成的评估量表"""
        data = self._load_distributions()
        assessments = data.get('assessments', [])
        
        # 过滤待完成的评估量表
        pending = [a for a in assessments if a.get('status') == 'pending']
        
        # 检查是否过期
        current_time = datetime.now()
        valid_assessments = []
        
        for assessment in pending:
            due_date_str = assessment.get('due_date')
            if due_date_str:
                try:
                    due_date = datetime.fromisoformat(due_date_str.replace('Z', '+00:00'))
                    if due_date.replace(tzinfo=None) > current_time:
                        valid_assessments.append(assessment)
                    else:
                        # 标记为过期
                        assessment['status'] = 'expired'
                except Exception as e:
                    logger.warning(f"解析到期时间失败: {due_date_str}, {str(e)}")
                    valid_assessments.append(assessment)  # 如果解析失败，仍然包含
            else:
                valid_assessments.append(assessment)
        
        # 如果有过期的评估量表，保存更新
        if len(valid_assessments) != len(pending):
            self._save_distributions(data)
        
        return valid_assessments
    
    def get_pending_questionnaires(self, user_id: Optional[str] = None) -> List[Dict]:
        """获取待完成的问卷"""
        data = self._load_distributions()
        questionnaires = data.get('questionnaires', [])
        
        # 过滤待完成的问卷
        pending = [q for q in questionnaires if q.get('status') == 'pending']
        
        # 检查是否过期
        current_time = datetime.now()
        valid_questionnaires = []
        
        for questionnaire in pending:
            due_date_str = questionnaire.get('due_date')
            if due_date_str:
                try:
                    due_date = datetime.fromisoformat(due_date_str.replace('Z', '+00:00'))
                    if due_date.replace(tzinfo=None) > current_time:
                        valid_questionnaires.append(questionnaire)
                    else:
                        # 标记为过期
                        questionnaire['status'] = 'expired'
                except Exception as e:
                    logger.warning(f"解析到期时间失败: {due_date_str}, {str(e)}")
                    valid_questionnaires.append(questionnaire)  # 如果解析失败，仍然包含
            else:
                valid_questionnaires.append(questionnaire)
        
        # 如果有过期的问卷，保存更新
        if len(valid_questionnaires) != len(pending):
            self._save_distributions(data)
        
        return valid_questionnaires
    
    def mark_assessment_completed(self, assessment_id, completion_data: Dict = None) -> bool:
        """标记评估量表为已完成"""
        try:
            data = self._load_distributions()
            index = self._find_assessment_index(data['assessments'], assessment_id)
            
            if index >= 0:
                data['assessments'][index]['status'] = 'completed'
                data['assessments'][index]['completed_at'] = datetime.now().isoformat()
                if completion_data:
                    data['assessments'][index]['completion_data'] = completion_data
                
                self._save_distributions(data)
                return True
            
            return False
        except Exception as e:
            logger.error(f"标记评估量表完成失败: {str(e)}")
            return False
    
    def mark_questionnaire_completed(self, questionnaire_id, completion_data: Dict = None) -> bool:
        """标记问卷为已完成"""
        try:
            data = self._load_distributions()
            index = self._find_questionnaire_index(data['questionnaires'], questionnaire_id)
            
            if index >= 0:
                data['questionnaires'][index]['status'] = 'completed'
                data['questionnaires'][index]['completed_at'] = datetime.now().isoformat()
                if completion_data:
                    data['questionnaires'][index]['completion_data'] = completion_data
                
                self._save_distributions(data)
                return True
            
            return False
        except Exception as e:
            logger.error(f"标记问卷完成失败: {str(e)}")
            return False

# 全局实例
_distribution_manager = None

def get_distribution_manager():
    """获取分发管理器实例（单例模式）"""
    global _distribution_manager
    if _distribution_manager is None:
        _distribution_manager = DistributionManager()
    return _distribution_manager
