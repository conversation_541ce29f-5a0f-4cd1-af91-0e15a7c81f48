#!/usr/bin/env python3
"""
测试分页格式修复
"""

import sys
import os

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def test_pagination_format_processing():
    """测试分页格式数据处理"""
    print("=== 测试分页格式数据处理 ===")
    
    # 模拟云端API返回的实际数据格式
    test_response = {
        "status": "success",
        "data": {
            "assessments": [
                {
                    "id": 5,
                    "name": "抑郁自评量表",
                    "assessment_type": "self",
                    "status": "pending",
                    "created_at": "2025-06-01T07:51:33",
                    "completed_at": None
                },
                {
                    "id": 3,
                    "name": "抑郁自评量表",
                    "assessment_type": "self", 
                    "status": "pending",
                    "created_at": "2025-06-01T07:08:55",
                    "completed_at": None
                }
            ],
            "total": 2,
            "skip": 0,
            "limit": 20
        }
    }
    
    def process_api_response(result):
        """模拟修复后的API响应处理逻辑"""
        assessment_list = []
        
        if isinstance(result, list):
            # 直接列表格式
            for item in result:
                if isinstance(item, dict):
                    assessment_list.append(item)
        elif isinstance(result, dict):
            if result.get('status') == 'success':
                data = result.get('data', [])
                
                # 检查是否是分页格式（包含assessments字段）
                if isinstance(data, dict) and 'assessments' in data:
                    # 分页格式：data.assessments包含实际的评估量表列表
                    assessments = data.get('assessments', [])
                    print(f"检测到分页格式，评估量表在assessments字段中，数量: {len(assessments)}")
                    
                    # 过滤掉非字典项
                    for item in assessments:
                        if isinstance(item, dict):
                            assessment_list.append(item)
                elif isinstance(data, list):
                    # 直接列表格式
                    for item in data:
                        if isinstance(item, dict):
                            assessment_list.append(item)
                else:
                    # 如果data不是列表也不是包含assessments的字典，包装为列表
                    if isinstance(data, dict):
                        assessment_list = [data]
        
        return assessment_list
    
    def extract_assessment_id(assessment):
        """ID提取逻辑"""
        return assessment.get('id') or assessment.get('distribution_id')
    
    # 测试处理
    processed_assessments = process_api_response(test_response)
    
    print(f"✓ 成功处理分页格式响应")
    print(f"✓ 提取到 {len(processed_assessments)} 个评估量表")
    
    # 验证每个评估量表
    for i, assessment in enumerate(processed_assessments):
        assessment_id = extract_assessment_id(assessment)
        assessment_name = assessment.get('name', '未命名')
        print(f"  评估量表 {i+1}: ID={assessment_id}, 名称={assessment_name}")
        
        if assessment_id is None:
            print(f"  ✗ 评估量表 {i+1} 的ID为None")
            return False
    
    print("✓ 所有评估量表都有有效的ID")
    return True

def test_different_response_formats():
    """测试不同的响应格式"""
    print("\n=== 测试不同的响应格式 ===")
    
    test_cases = [
        {
            "name": "分页格式",
            "response": {
                "status": "success",
                "data": {
                    "assessments": [
                        {"id": 1, "name": "量表1"},
                        {"id": 2, "name": "量表2"}
                    ],
                    "total": 2
                }
            },
            "expected_count": 2,
            "expected_ids": [1, 2]
        },
        {
            "name": "直接列表格式",
            "response": {
                "status": "success",
                "data": [
                    {"id": 3, "name": "量表3"},
                    {"id": 4, "name": "量表4"}
                ]
            },
            "expected_count": 2,
            "expected_ids": [3, 4]
        },
        {
            "name": "单个对象格式",
            "response": {
                "status": "success",
                "data": {"id": 5, "name": "量表5"}
            },
            "expected_count": 1,
            "expected_ids": [5]
        },
        {
            "name": "空响应",
            "response": {
                "status": "success",
                "data": {
                    "assessments": [],
                    "total": 0
                }
            },
            "expected_count": 0,
            "expected_ids": []
        }
    ]
    
    def process_api_response(result):
        """处理API响应"""
        assessment_list = []
        
        if isinstance(result, list):
            for item in result:
                if isinstance(item, dict):
                    assessment_list.append(item)
        elif isinstance(result, dict):
            if result.get('status') == 'success':
                data = result.get('data', [])
                
                if isinstance(data, dict) and 'assessments' in data:
                    assessments = data.get('assessments', [])
                    for item in assessments:
                        if isinstance(item, dict):
                            assessment_list.append(item)
                elif isinstance(data, list):
                    for item in data:
                        if isinstance(item, dict):
                            assessment_list.append(item)
                else:
                    if isinstance(data, dict):
                        assessment_list = [data]
        
        return assessment_list
    
    def extract_assessment_id(assessment):
        """ID提取逻辑"""
        return assessment.get('id') or assessment.get('distribution_id')
    
    passed = 0
    total = len(test_cases)
    
    for test_case in test_cases:
        response = test_case["response"]
        expected_count = test_case["expected_count"]
        expected_ids = test_case["expected_ids"]
        
        processed_assessments = process_api_response(response)
        actual_count = len(processed_assessments)
        actual_ids = [extract_assessment_id(a) for a in processed_assessments]
        
        if actual_count == expected_count and actual_ids == expected_ids:
            print(f"✓ {test_case['name']}: 数量={actual_count}, IDs={actual_ids}")
            passed += 1
        else:
            print(f"✗ {test_case['name']}")
            if actual_count != expected_count:
                print(f"  数量错误: 期望={expected_count}, 实际={actual_count}")
            if actual_ids != expected_ids:
                print(f"  ID错误: 期望={expected_ids}, 实际={actual_ids}")
    
    print(f"\n测试结果: {passed}/{total} 个测试通过")
    return passed == total

def test_error_handling():
    """测试错误处理"""
    print("\n=== 测试错误处理 ===")
    
    error_cases = [
        {
            "name": "错误状态响应",
            "response": {
                "status": "error",
                "message": "服务器错误"
            },
            "expected_count": 0
        },
        {
            "name": "无效数据格式",
            "response": {
                "status": "success",
                "data": "这是一个字符串，不是预期的格式"
            },
            "expected_count": 0
        },
        {
            "name": "包含非字典项的列表",
            "response": {
                "status": "success",
                "data": {
                    "assessments": [
                        {"id": 1, "name": "有效量表"},
                        "这是一个字符串",
                        {"id": 2, "name": "另一个有效量表"},
                        None,
                        {"id": 3, "name": "第三个有效量表"}
                    ],
                    "total": 5
                }
            },
            "expected_count": 3  # 只有3个有效的字典项
        }
    ]
    
    def process_api_response(result):
        """处理API响应"""
        assessment_list = []
        
        if isinstance(result, list):
            for item in result:
                if isinstance(item, dict):
                    assessment_list.append(item)
        elif isinstance(result, dict):
            if result.get('status') == 'success':
                data = result.get('data', [])
                
                if isinstance(data, dict) and 'assessments' in data:
                    assessments = data.get('assessments', [])
                    for item in assessments:
                        if isinstance(item, dict):
                            assessment_list.append(item)
                elif isinstance(data, list):
                    for item in data:
                        if isinstance(item, dict):
                            assessment_list.append(item)
                else:
                    if isinstance(data, dict):
                        assessment_list = [data]
        
        return assessment_list
    
    passed = 0
    total = len(error_cases)
    
    for test_case in error_cases:
        response = test_case["response"]
        expected_count = test_case["expected_count"]
        
        try:
            processed_assessments = process_api_response(response)
            actual_count = len(processed_assessments)
            
            if actual_count == expected_count:
                print(f"✓ {test_case['name']}: 正确处理，数量={actual_count}")
                passed += 1
            else:
                print(f"✗ {test_case['name']}: 数量错误 (期望={expected_count}, 实际={actual_count})")
        except Exception as e:
            print(f"✗ {test_case['name']}: 处理异常 - {e}")
    
    print(f"\n测试结果: {passed}/{total} 个测试通过")
    return passed == total

def main():
    """主测试函数"""
    print("开始测试分页格式修复...\n")
    
    test1_passed = test_pagination_format_processing()
    test2_passed = test_different_response_formats()
    test3_passed = test_error_handling()
    
    if test1_passed and test2_passed and test3_passed:
        print("\n🎉 所有测试通过！")
        print("\n修复说明:")
        print("1. 正确处理分页格式响应（data.assessments）")
        print("2. 支持多种响应格式（直接列表、单个对象、分页格式）")
        print("3. 过滤掉非字典类型的数据项")
        print("4. 正确提取评估量表ID")
        print("5. 优雅处理错误和异常情况")
        print("\n现在移动端应该能正确处理云端返回的分页格式数据。")
        return True
    else:
        print("\n❌ 部分测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
