import os
import json
import time
import logging
import threading
from threading import Lock

import requests

# 配置日志 - 使用简单的日志配置
logger = logging.getLogger(__name__)
# 避免在模块级别调用任何可能导致递归的日志方法

# 缓存实例
_cloud_api_instance = None

def get_cloud_api(base_url=None, backup_url=None, auto_switch=True):
    """获取CloudAPI实例，单例模式

    Args:
        base_url: 基础URL，可选
        backup_url: 备用URL，可选
        auto_switch: 是否在公网服务器失败时自动切换到本地服务器，默认True

    Returns:
        CloudAPI: CloudAPI实例
    """
    global _cloud_api_instance

    if _cloud_api_instance is None:
        _cloud_api_instance = CloudAPI(base_url=base_url, auto_switch=auto_switch)

        # 如果提供了备用URL，设置它
        if backup_url:
            _cloud_api_instance.backup_url = backup_url
    elif base_url:
        # 如果实例已存在，但提供了新的base_url，则更新
        _cloud_api_instance.base_url = base_url
        # 如果提供了备用URL，更新它
        if backup_url:
            _cloud_api_instance.backup_url = backup_url

        # 更新自动切换设置
        _cloud_api_instance.auto_switch = auto_switch

    return _cloud_api_instance

class CloudAPI:
    """云API客户端"""

    def __init__(self, base_url=None, timeout=30, retry_count=3, auto_switch=True):
        """初始化API客户端

        Args:
            base_url: API基础URL，为None时使用默认地址
            timeout: 请求超时时间（秒）
            retry_count: 最大重试次数
            auto_switch: 是否在公网服务器失败时自动切换到本地服务器
        """
        # 服务器配置
        self.server_configs = [
            {
                "name": "公网服务器",
                "url": "http://************:80",  # 公网服务器使用80端口
                "priority": 1,  # 公网服务器优先级最高
                "available": True,  # 是否可用
                "last_check": 0,  # 上次检查时间
                "failure_count": 0  # 连续失败次数
            },
            {
                "name": "本地服务器",
                "url": "http://localhost:8006",  # 本地服务器使用8006端口
                "priority": 2,  # 优先级，数字越小优先级越高
                "available": True,  # 是否可用
                "last_check": 0,  # 上次检查时间
                "failure_count": 0  # 连续失败次数
            },
            {
                "name": "本地回环地址",
                "url": "http://127.0.0.1:8006",  # 修改为8006端口
                "priority": 3,
                "available": True,
                "last_check": 0,
                "failure_count": 0
            }
        ]

        # 设置当前使用的服务器
        self.current_server_index = 0  # 默认使用第一个服务器（本地服务器）

        # 如果提供了base_url，则将其添加为最高优先级的服务器
        if base_url:
            self.server_configs.insert(0, {
                "name": "自定义服务器",
                "url": base_url,
                "priority": 0,
                "available": True,
                "last_check": 0,
                "failure_count": 0
            })

        # 默认配置
        self.base_url = self.server_configs[self.current_server_index]["url"]
        self.backup_url = self.server_configs[1]["url"] if len(self.server_configs) > 1 else None
        self.timeout = timeout
        self.auto_switch = auto_switch  # 添加自动切换标志

        # 用户认证相关属性
        self.token = None
        self.user_id = None
        self.custom_id = None  # 添加custom_id属性，用于文件上传时关联用户
        self.retry_count = retry_count

        # 记录当前使用的服务器名称
        self.current_server_name = self.server_configs[self.current_server_index]["name"]

        # 认证相关
        self.token = None
        self.refresh_token_str = None
        self.expires_at = None
        self.user_id = None

        # 错误信息
        self.last_error = None

        # 降级模式相关
        self.degraded_mode = False
        self.degraded_mode_until = 0
        self.server_failure_count = 0
        self.using_backup_url = False

        # 支持的文件格式
        self.supported_formats = ['.jpg', '.jpeg', '.png', '.pdf', '.doc', '.docx', '.xls', '.xlsx']

        # 确保目录存在
        self.data_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data')
        self.queue_dir = os.path.join(self.data_dir, 'upload_queue')

        # 创建必要的目录
        if not os.path.exists(self.data_dir):
            try:
                os.makedirs(self.data_dir)
                logger.info(f"创建数据目录: {self.data_dir}")
            except Exception as e:
                logger.error(f"创建数据目录失败: {str(e)}")

        # 确保上传队列目录存在
        if not os.path.exists(self.queue_dir):
            try:
                os.makedirs(self.queue_dir, exist_ok=True)
                logger.info(f"创建上传队列目录: {self.queue_dir}")
            except Exception as e:
                logger.error(f"创建上传队列目录失败: {str(e)}")

        # 加载认证信息
        self.load_auth_info()

        # 记录到日志（使用debug级别减少重复日志）
        logger.debug(f"初始化API客户端，基础URL: {self.base_url}")
        logger.debug(f"文件上传超时设置: {self.timeout}秒")

        # 锁，用于保护访问令牌
        self.auth_lock = Lock()
        self._token_lock = threading.Lock()  # 添加锁来保护token访问

    def _make_request(self, method, endpoint, data=None, json_data=None, headers=None, bypass_auth=False, files=None, max_retries=3, params=None):
        """发送HTTP请求并处理响应

        Args:
            method: HTTP方法（GET, POST等）
            endpoint: API端点路径
            data: 表单数据
            json_data: JSON数据
            headers: 请求头
            bypass_auth: 是否绕过认证
            files: 文件上传
            max_retries: 最大重试次数
            params: URL查询参数

        Returns:
            dict: API响应，解析为JSON格式
        """
        # 初始化请求头
        if headers is None:
            headers = {}

        # 如果不是绕过认证的请求，且Token已设置，则添加认证头
        if not bypass_auth and self.token:
            # 使用辅助方法确保令牌格式正确
            if self._ensure_token_format():
                # 令牌格式正确或已成功修复
                headers["Authorization"] = f"Bearer {self.token}"
                logger.debug(f"添加Bearer认证头: {self.token[:10]}...")
            else:
                # 令牌格式无法修复
                logger.warning("令牌格式无法修复，不使用Bearer认证")
                # 如果令牌无法修复，不添加Authorization头，将依赖X-User-ID认证
        else:
            logger.debug(f"未添加Bearer认证头，token为空或绕过认证")

        # 无论是否有token，只要有custom_id，都添加X-User-ID头
        if not bypass_auth and hasattr(self, 'custom_id') and self.custom_id:
            headers["X-User-ID"] = self.custom_id
            logger.info(f"添加X-User-ID头: {self.custom_id}")

        # 拼接完整URL
        current_url = self.base_url
        if not current_url.endswith('/api'):
            current_url = current_url + '/api' if not current_url.endswith('/') else current_url + 'api'

        url = f"{current_url}/{endpoint}" if not endpoint.startswith('/') else f"{current_url}{endpoint}"

        # 设置失败后自动切换服务器的逻辑
        retries = 0
        last_error = None

        while retries <= max_retries:
            try:
                # 记录请求信息
                logger.debug(f"发送 {method} 请求 [{url}]")

                # 发送请求，强制禁用所有代理
                # 检查requests版本是否支持trust_env参数
                request_kwargs = {
                    'method': method,
                    'url': url,
                    'params': params,
                    'data': data,
                    'json': json_data,
                    'headers': headers,
                    'files': files,
                    'timeout': self.timeout,
                    'proxies': {
                        'http': None,
                        'https': None,
                        'ftp': None,
                        'socks4': None,
                        'socks5': None
                    }
                }

                # 尝试添加trust_env参数，如果不支持则忽略
                try:
                    import inspect
                    sig = inspect.signature(requests.request)
                    if 'trust_env' in sig.parameters:
                        request_kwargs['trust_env'] = False
                except Exception:
                    # 如果检查失败，不添加trust_env参数
                    pass

                response = requests.request(**request_kwargs)

                # 检查HTTP状态码
                if response.status_code >= 200 and response.status_code < 300:
                    # 重置失败计数
                    self.server_configs[self.current_server_index]["failure_count"] = 0

                    # 尝试解析JSON响应
                    try:
                        return response.json()
                    except ValueError:
                        # 如果响应不是JSON格式，返回文本内容
                        return {"status": "success", "message": response.text}

                elif response.status_code >= 500:
                    # 服务器错误，记录失败并考虑切换服务器
                    self.server_configs[self.current_server_index]["failure_count"] += 1
                    error_msg = f"服务器返回错误状态码: {response.status_code}"
                    logger.error(error_msg)

                    # 如果启用了自动切换，尝试切换服务器
                    if self.auto_switch and self._should_switch_server():
                        logger.info(f"尝试切换服务器，当前服务器: {self.current_server_name}")
                        self._switch_server(self.current_server_index)
                        # 更新URL和当前服务器名称
                        current_url = self.base_url
                        if not current_url.endswith('/api'):
                            current_url = current_url + '/api' if not current_url.endswith('/') else current_url + 'api'
                        url = f"{current_url}/{endpoint}" if not endpoint.startswith('/') else f"{current_url}{endpoint}"
                        logger.info(f"已切换到服务器: {self.current_server_name}, 新URL: {url}")
                        retries += 1
                        continue

                    # 最后一次重试，返回错误响应
                    if retries == max_retries:
                        try:
                            return response.json()
                        except ValueError:
                            return {"status": "error", "message": error_msg}

                else:
                    # 客户端错误
                    error_msg = f"客户端错误，状态码: {response.status_code}"
                    logger.error(error_msg)

                    # 尝试解析错误响应
                    try:
                        return response.json()
                    except ValueError:
                        return {"status": "error", "message": error_msg, "detail": response.text}

            except requests.exceptions.RequestException as e:
                # 网络错误，考虑切换服务器
                error_msg = f"请求异常: {str(e)}"
                logger.error(error_msg)
                last_error = e

                # 检查是否是代理相关错误
                if "proxy" in str(e).lower() or "127.0.0.1:7890" in str(e):
                    logger.warning("检测到代理相关错误，尝试修复代理设置")
                    try:
                        from .proxy_config import fix_proxy_issues
                        if fix_proxy_issues():
                            logger.info("代理问题已修复，将在下次重试时生效")
                    except Exception as proxy_fix_error:
                        logger.error(f"修复代理设置失败: {proxy_fix_error}")

                # 记录失败并考虑切换服务器
                self.server_configs[self.current_server_index]["failure_count"] += 1

                # 如果启用了自动切换，尝试切换服务器
                if self.auto_switch and self._should_switch_server():
                    logger.info(f"尝试切换服务器，当前服务器: {self.current_server_name}")
                    self._switch_server(self.current_server_index)
                    # 更新URL和当前服务器名称
                    current_url = self.base_url
                    if not current_url.endswith('/api'):
                        current_url = current_url + '/api' if not current_url.endswith('/') else current_url + 'api'
                    url = f"{current_url}/{endpoint}" if not endpoint.startswith('/') else f"{current_url}{endpoint}"
                    logger.info(f"已切换到服务器: {self.current_server_name}, 新URL: {url}")
                    retries += 1
                    continue

            # 增加重试次数
            retries += 1

        # 所有重试都失败
        return {"status": "error", "message": f"请求失败，已重试{max_retries}次", "detail": str(last_error)}

    def _should_switch_server(self):
        """判断是否应该切换服务器"""
        # 如果当前服务器连续失败次数超过阈值，应该切换
        current_failures = self.server_configs[self.current_server_index]["failure_count"]
        return current_failures >= 2  # 连续失败2次以上时切换服务器

    def request_ocr(self, file_id, options=None):
        """
        请求后端对指定文件进行OCR处理。
        Args:
            file_id (str|int): 文档ID
            options (dict): OCR参数，如语言、方向检测等
        Returns:
            dict: 后端返回的OCR任务信息
        """
        endpoint = f"documents/{file_id}/ocr"
        headers = {}
        if self.token:
            headers['Authorization'] = f"Bearer {self.token}"
        elif self.custom_id:
            headers['X-User-ID'] = str(self.custom_id)
        # 兼容部分后端只认X-User-ID
        if self.custom_id:
            headers['X-User-ID'] = str(self.custom_id)
        try:
            logger.info(f"请求后端OCR: file_id={file_id}, options={options}")
            resp = self._make_request(
                method="POST",
                endpoint=endpoint,
                json_data=options or {},
                headers=headers,
                timeout=60
            )
            if resp and (resp.get('success') or resp.get('status') == 'success'):
                logger.info(f"OCR处理请求成功: {file_id}")
                return resp.get('data', resp)
            else:
                error_msg = resp.get('message', 'OCR请求失败，未知错误') if resp else 'OCR请求失败'
                logger.error(error_msg)
                self.last_error = error_msg
                return None
        except Exception as e:
            logger.error(f"请求OCR处理异常: {e}")
            self.last_error = str(e)
            return None

    def get_documents(self, page=1, page_size=20, document_type=None, custom_id=None):
        """获取文档列表

        Args:
            page (int): 页码，从1开始
            page_size (int): 每页数量
            document_type (str, optional): 文档类型过滤
            custom_id (str, optional): 用户自定义ID，用于过滤当前用户文档

        Returns:
            dict: 文档列表，包含documents和total字段
        """
        # 检查认证状态
        if not self.token:
            # 尝试加载认证信息
            self.load_auth_info()

            # 再次检查认证状态 - 修改为同时检查token和custom_id
            if not self.token and not hasattr(self, 'custom_id'):
                logger.error("未登录，无法获取文档列表")
                self.last_error = "未登录，无法获取文档列表"
                return None
            elif not self.token and hasattr(self, 'custom_id') and self.custom_id:
                logger.info(f"没有token但有custom_id: {self.custom_id}，可以使用X-User-ID认证")
                # 继续处理，将使用X-User-ID认证

        # 准备自定义头部，确保包含X-User-ID
        custom_headers = {}
        if hasattr(self, 'custom_id') and self.custom_id:
            custom_headers["X-User-ID"] = self.custom_id
            logger.info(f"添加X-User-ID头部用于获取文档列表: {self.custom_id}")

        # 准备查询参数
        params = {
            'page': page,
            'page_size': page_size
        }

        if document_type:
            params['document_type'] = document_type

        # 强制将 custom_id 作为查询参数传递，确保后端只返回当前用户文档
        if custom_id:
            params['custom_id'] = custom_id
            logger.info(f"查询参数中添加 custom_id: {custom_id}")
        elif hasattr(self, 'custom_id') and self.custom_id:
            params['custom_id'] = self.custom_id
            logger.info(f"查询参数中添加实例 custom_id: {self.custom_id}")

        # 发送获取文档列表请求
        logger.info(f"获取文档列表: 页码={page}, 每页数量={page_size}, 文档类型={document_type}")

        # 尝试使用新的API路径
        try:
            result = self._make_request(
                method="GET",
                endpoint="documents",  # 使用正确的端点路径
                params=params,  # 添加查询参数
                headers=custom_headers,  # 添加自定义头部
                max_retries=3  # 设置重试次数
            )
        except Exception as e:
            logger.error(f"使用标准API路径获取文档列表失败: {str(e)}")
            # 尝试使用备用API路径
            try:
                result = self._make_request(
                    method="GET",
                    endpoint="api/documents",  # 尝试使用可能的备用路径
                    params=params,
                    headers=custom_headers,
                    max_retries=3
                )
            except Exception as e2:
                logger.error(f"使用备用API路径获取文档列表也失败: {str(e2)}")
                result = None

        if result:
            # 检查不同的成功响应格式
            if result.get("success") or result.get("status") == "success":
                # 提取数据，兼容不同的响应格式
                data = result.get("data", {})
                if not data and isinstance(result, dict):
                    # 如果没有data字段，但result本身是字典，可能整个result就是数据
                    data = result

                # 确保返回的数据包含documents字段
                if "documents" not in data and "items" in data:
                    data["documents"] = data["items"]

                logger.info(f"成功获取文档列表，共 {len(data.get('documents', []))} 个文档")
                return data
            else:
                error_msg = result.get("message", "获取文档列表失败，未知错误")
                logger.error(f"获取文档列表失败: {error_msg}")
                self.last_error = error_msg
                return None
        else:
            error_msg = "获取文档列表失败，无法连接服务器"
            logger.error(error_msg)
            self.last_error = error_msg
            return None

    def get_mobile_assessments(self, custom_id=None):
        """获取移动端分发的评估量表

        Args:
            custom_id: 用户自定义ID

        Returns:
            dict: 评估量表列表
        """
        # 准备请求头
        headers = {}
        if self.token:
            headers['Authorization'] = f"Bearer {self.token}"
        if custom_id or self.custom_id:
            headers['X-User-ID'] = custom_id or self.custom_id
        headers['Content-Type'] = 'application/json'

        # 准备查询参数
        params = {}
        if custom_id or self.custom_id:
            params['custom_id'] = custom_id or self.custom_id

        logger.info(f"获取移动端评估量表，custom_id: {custom_id or self.custom_id}")

        try:
            result = self._make_request(
                method="GET",
                endpoint="mobile/assessments",
                params=params,
                headers=headers,
                max_retries=3
            )

            if result and (result.get('status') == 'success' or result.get('success')):
                logger.info("成功获取移动端评估量表")

                # 检查并处理数据格式，确保所有评估量表都是字典格式
                data = result.get('data', [])
                if isinstance(data, dict) and 'assessments' in data:
                    assessments = data.get('assessments', [])
                elif isinstance(data, list):
                    assessments = data
                else:
                    assessments = []

                # 过滤并验证评估量表数据
                filtered_assessments = []
                for assessment in assessments:
                    if isinstance(assessment, dict):
                        # 确保所有字段都使用字典访问方式，避免template_id属性错误
                        try:
                            # 验证必要字段是否存在
                            assessment_id = (assessment.get('id') or
                                           assessment.get('assessment_id') or
                                           assessment.get('template_id') or
                                           assessment.get('distribution_id'))

                            if assessment_id:
                                filtered_assessments.append(assessment)
                                logger.debug(f"添加评估量表: {assessment.get('name', '未命名')} (ID: {assessment_id})")
                            else:
                                logger.warning(f"跳过无ID的评估量表: {assessment}")
                        except Exception as item_error:
                            logger.error(f"处理评估量表项时出错: {str(item_error)}, 数据: {assessment}")
                            continue
                    else:
                        logger.warning(f"跳过非字典类型的评估量表项: {type(assessment)} - {assessment}")

                # 更新结果数据
                if isinstance(result.get('data'), dict) and 'assessments' in result['data']:
                    result['data']['assessments'] = filtered_assessments
                else:
                    result['data'] = filtered_assessments

                logger.info(f"成功处理 {len(filtered_assessments)} 个有效评估量表")
                return result
            else:
                error_msg = result.get('message', '获取评估量表失败') if result else '请求失败'
                logger.error(f"获取移动端评估量表失败: {error_msg}")
                self.last_error = error_msg
                return {"status": "error", "message": error_msg}

        except Exception as e:
            error_msg = f"获取移动端评估量表异常: {str(e)}"
            logger.error(error_msg)
            # 检查是否是template_id相关的错误
            if "template_id" in str(e) and "attribute" in str(e):
                error_msg = f"获取用户评估量表失败: {str(e)}"
                logger.error(f"检测到template_id属性访问错误，可能是后端数据格式问题: {error_msg}")
            self.last_error = error_msg
            return {"status": "error", "message": error_msg}

    def get_mobile_questionnaires(self, custom_id=None):
        """获取移动端分发的问卷

        Args:
            custom_id: 用户自定义ID

        Returns:
            dict: 问卷列表
        """
        # 准备请求头
        headers = {}
        if self.token:
            headers['Authorization'] = f"Bearer {self.token}"
        if custom_id or self.custom_id:
            headers['X-User-ID'] = custom_id or self.custom_id
        headers['Content-Type'] = 'application/json'

        # 准备查询参数
        params = {}
        if custom_id or self.custom_id:
            params['custom_id'] = custom_id or self.custom_id

        logger.info(f"获取移动端问卷，custom_id: {custom_id or self.custom_id}")

        try:
            result = self._make_request(
                method="GET",
                endpoint="mobile/questionnaires",
                params=params,
                headers=headers,
                max_retries=3
            )

            if result and (result.get('status') == 'success' or result.get('success')):
                logger.info("成功获取移动端问卷")

                # 检查并处理数据格式，确保所有问卷都是字典格式
                data = result.get('data', [])
                if isinstance(data, dict) and 'questionnaires' in data:
                    questionnaires = data.get('questionnaires', [])
                elif isinstance(data, list):
                    questionnaires = data
                else:
                    questionnaires = []

                # 过滤并验证问卷数据
                filtered_questionnaires = []
                for questionnaire in questionnaires:
                    if isinstance(questionnaire, dict):
                        # 确保所有字段都使用字典访问方式
                        try:
                            # 验证必要字段是否存在
                            questionnaire_id = (questionnaire.get('id') or
                                              questionnaire.get('questionnaire_id') or
                                              questionnaire.get('template_id') or
                                              questionnaire.get('distribution_id'))

                            if questionnaire_id:
                                filtered_questionnaires.append(questionnaire)
                                logger.debug(f"添加问卷: {questionnaire.get('name', '未命名')} (ID: {questionnaire_id})")
                            else:
                                logger.warning(f"跳过无ID的问卷: {questionnaire}")
                        except Exception as item_error:
                            logger.error(f"处理问卷项时出错: {str(item_error)}, 数据: {questionnaire}")
                            continue
                    else:
                        logger.warning(f"跳过非字典类型的问卷项: {type(questionnaire)} - {questionnaire}")

                # 更新结果数据
                if isinstance(result.get('data'), dict) and 'questionnaires' in result['data']:
                    result['data']['questionnaires'] = filtered_questionnaires
                else:
                    result['data'] = filtered_questionnaires

                logger.info(f"成功处理 {len(filtered_questionnaires)} 个有效问卷")
                return result
            else:
                error_msg = result.get('message', '获取问卷失败') if result else '请求失败'
                logger.error(f"获取移动端问卷失败: {error_msg}")
                self.last_error = error_msg
                return {"status": "error", "message": error_msg}

        except Exception as e:
            error_msg = f"获取移动端问卷异常: {str(e)}"
            logger.error(error_msg)
            # 检查是否是template_id相关的错误
            if "template_id" in str(e) and "attribute" in str(e):
                error_msg = f"获取用户问卷失败: {str(e)}"
                logger.error(f"检测到template_id属性访问错误，可能是后端数据格式问题: {error_msg}")
            self.last_error = error_msg
            return {"status": "error", "message": error_msg}

    def submit_mobile_assessment(self, assessment_id, answers, custom_id=None):
        """提交移动端评估量表结果

        Args:
            assessment_id: 评估量表ID
            answers: 答案列表
            custom_id: 用户自定义ID

        Returns:
            dict: 提交结果
        """
        # 准备请求头
        headers = {}
        if self.token:
            headers['Authorization'] = f"Bearer {self.token}"
        if custom_id or self.custom_id:
            headers['X-User-ID'] = custom_id or self.custom_id
        headers['Content-Type'] = 'application/json'

        # 准备提交数据
        submit_data = {
            "answers": answers
        }

        logger.info(f"提交移动端评估量表结果，assessment_id: {assessment_id}, custom_id: {custom_id or self.custom_id}")

        try:
            # 使用新的移动端API端点
            result = self._make_request(
                method="POST",
                endpoint=f"mobile/assessments/{assessment_id}/submit",
                json_data=submit_data,
                headers=headers,
                max_retries=3
            )

            if result and (result.get('status') == 'success' or result.get('success')):
                logger.info("成功提交移动端评估量表结果")
                return result
            else:
                error_msg = result.get('message', '提交评估结果失败') if result else '请求失败'
                logger.error(f"提交移动端评估量表结果失败: {error_msg}")
                self.last_error = error_msg
                return {"status": "error", "message": error_msg}

        except Exception as e:
            error_msg = f"提交移动端评估量表结果异常: {str(e)}"
            logger.error(error_msg)
            self.last_error = error_msg
            return {"status": "error", "message": error_msg}

    def submit_mobile_questionnaire(self, questionnaire_id, answers, custom_id=None):
        """提交移动端问卷结果

        Args:
            questionnaire_id: 问卷ID
            answers: 答案列表
            custom_id: 用户自定义ID

        Returns:
            dict: 提交结果
        """
        # 准备请求头
        headers = {}
        if self.token:
            headers['Authorization'] = f"Bearer {self.token}"
        if custom_id or self.custom_id:
            headers['X-User-ID'] = custom_id or self.custom_id
        headers['Content-Type'] = 'application/json'

        # 准备提交数据
        submit_data = {
            "answers": answers
        }

        logger.info(f"提交移动端问卷结果，questionnaire_id: {questionnaire_id}, custom_id: {custom_id or self.custom_id}")

        try:
            result = self._make_request(
                method="POST",
                endpoint=f"mobile/questionnaires/{questionnaire_id}/submit",
                json_data=submit_data,
                headers=headers,
                max_retries=3
            )

            if result and (result.get('status') == 'success' or result.get('success')):
                logger.info("成功提交移动端问卷结果")
                return result
            else:
                error_msg = result.get('message', '提交问卷结果失败') if result else '请求失败'
                logger.error(f"提交移动端问卷结果失败: {error_msg}")
                self.last_error = error_msg
                return {"status": "error", "message": error_msg}

        except Exception as e:
            error_msg = f"提交移动端问卷结果异常: {str(e)}"
            logger.error(error_msg)
            self.last_error = error_msg
            return {"status": "error", "message": error_msg}

    def authenticate(self, username, password=None, password_hash=None, force_cloud_auth=False):
        """用户身份验证

        Args:
            username: 用户名
            password: 密码明文，与password_hash二选一
            password_hash: 密码哈希值，与password二选一
            force_cloud_auth: 是否强制使用云端认证，忽略降级模式

        Returns:
            dict: 认证结果，成功时包含认证令牌
        """
        # 导入API适配器
        from .api_adapters import adapt_login_data

        # 先检查服务器健康状态
        server_health = self.check_server_health()
        if not server_health.get('online', False) and not force_cloud_auth:
            logger.warning(f"服务器离线，跳过云端认证")
            return None

        # 先检查是否处于降级模式
        if self.degraded_mode and not force_cloud_auth:
            logger.warning("处于降级模式，无法进行在线认证")
            return None

        # 确保用户名和密码(或哈希)至少一个有值
        if not username or (not password and not password_hash):
            logger.error("用户名和密码(或哈希)不能为空")
            return None

        try:
            # 准备认证数据
            auth_data = {
                "username": username
            }

            # 优先使用密码哈希
            if password_hash:
                auth_data["password"] = password_hash
                logger.debug(f"使用密码哈希进行认证: {username}")
            elif password:
                # 对密码进行哈希处理以增强安全性
                # 在实际情况下，应该在客户端进行哈希处理，这里简化处理
                auth_data["password"] = password
                logger.debug(f"使用明文密码进行认证: {username}")
            else:
                logger.error("密码或密码哈希必须提供一个")
                return None

            # 发送认证请求 - 使用正确的端点路径
            response = self._make_request(
                "POST",
                "auth/login",  # 移除前导/api，因为_make_request会自动添加
                data=auth_data,
                bypass_auth=True  # 认证请求不需要携带认证信息
            )

            # 检查响应
            if response.get("status") == "error":
                logger.error(f"认证失败: {response.get('message')}")
                return response

            # 处理认证成功的情况
            access_token = response.get("access_token", "")
            token_type = response.get("token_type", "").lower()

            if access_token and token_type == "bearer":
                # 使用辅助方法确保令牌格式正确
                self.token = access_token  # 先设置token
                if not self._ensure_token_format():
                    logger.error(f"服务器返回的令牌格式异常且无法修复，可能导致后续请求认证失败")
                    # 尽管格式有问题，我们仍然继续处理，但记录警告

                # 获取清理后的令牌
                clean_token = self.token

                # 保存令牌
                self.token = clean_token

                # 获取用户信息
                user_info = response.get("user")
                user_id = None
                custom_id = None

                if isinstance(user_info, dict):
                    user_id = user_info.get("id")

                    # 检查是否有custom_id
                    custom_id = user_info.get("custom_id")
                    if not custom_id:
                        logger.warning("后端返回的用户信息中没有custom_id，这可能导致某些功能无法正常工作")

                    logger.info(f"认证成功，获取到用户信息: {user_info.get('username')}, ID: {user_id}, custom_id: {custom_id}")

                # 保存用户ID和custom_id到实例变量
                self.user_id = user_id
                self.custom_id = custom_id
                logger.info(f"保存用户ID: {user_id} 和custom_id: {custom_id}到CloudAPI实例")

                # 保存认证信息到本地
                self._save_auth_info(clean_token, user_id)

                # 返回认证结果
                return {
                    "status": "success",
                    "token": clean_token,
                    "user_id": user_id,
                    "custom_id": custom_id,  # 添加custom_id
                    "user_info": user_info
                }
            else:
                logger.error(f"认证成功但未获取到有效令牌: {response}")
                return {
                    "status": "error",
                    "message": "未获取到有效令牌",
                    "data": response
                }

        except Exception as e:
            logger.exception(f"认证过程出错: {str(e)}")
            return {
                "status": "error",
                "message": f"认证失败: {str(e)}"
            }

    def refresh_token(self):
        """刷新认证token"""
        if not self.token:
            logger.error("没有token可刷新")
            self.last_error = "没有token可刷新"
            return False

        # 根据API文档，刷新token应使用JSON格式
        # 发送刷新请求
        result = self._make_request(
            method="POST",
            endpoint="auth/refresh",
            json_data={"refresh_token": self.refresh_token_str} if self.refresh_token_str else {},
            headers={"Authorization": f"Bearer {self.token}"},
            bypass_auth=True
        )

        if result and result.get("success"):
            # 更新token信息
            resp_data = result.get("data", {})
            self.token = resp_data.get("token", self.token)
            self.refresh_token_str = resp_data.get("refresh_token", self.refresh_token_str)
            self.expires_at = resp_data.get("expires_at", self.expires_at)

            # 保存更新后的认证信息
            self.save_auth_info()

            return True

        return False

    def logout(self):
        """用户退出登录"""
        if not self.token:
            return True

        # 发送登出请求
        self._make_request(
            method="POST",
            endpoint="auth/logout",
            headers={"Authorization": f"Bearer {self.token}"}
        )

        # 无论服务器返回什么，都清除本地token
        self.token = None
        self.refresh_token_str = None
        self.expires_at = None
        self.user_id = None

        # 清除保存的认证信息
        try:
            auth_file = os.path.join(self.data_dir, 'cloud_auth.json')
            if os.path.exists(auth_file):
                os.remove(auth_file)
                logger.info("已删除认证信息文件")
        except Exception as e:
            logger.error(f"删除认证信息文件失败: {str(e)}")

        return True

    def save_auth_info(self):
        """保存认证信息到文件"""
        if not self.token:
            return

        auth_info = {
            "token": self.token,
            "refresh_token": self.refresh_token_str,
            "user_id": self.user_id,
            "expires_at": self.expires_at
        }

        # 添加custom_id字段（如果有值）
        if hasattr(self, 'custom_id') and self.custom_id:
            auth_info["custom_id"] = self.custom_id
            logger.info(f"保存custom_id到认证信息: {self.custom_id}")

        try:
            # 使用统一的文件名 cloud_auth.json
            auth_file = os.path.join(self.data_dir, 'cloud_auth.json')
            with open(auth_file, 'w', encoding='utf-8') as f:
                json.dump(auth_info, f, ensure_ascii=False)
            logger.info(f"已保存认证信息，包含custom_id: {self.custom_id if hasattr(self, 'custom_id') else 'None'}")
        except Exception as e:
            logger.error(f"保存认证信息失败: {str(e)}")

    def load_auth_info(self):
        """从文件加载认证信息"""
        try:
            # 使用统一的文件名 cloud_auth.json
            auth_file = os.path.join(self.data_dir, 'cloud_auth.json')
            if os.path.exists(auth_file):
                with open(auth_file, 'r', encoding='utf-8') as f:
                    auth_info = json.load(f)

                self.token = auth_info.get("token")
                self.refresh_token_str = auth_info.get("refresh_token")
                self.user_id = auth_info.get("user_id")
                self.custom_id = auth_info.get("custom_id")  # 加载custom_id字段
                self.expires_at = auth_info.get("expires_at")

                # 记录详细的认证信息，便于调试
                logger.info(f"从文件加载认证信息 - token: {bool(self.token)}, refresh_token: {bool(self.refresh_token_str)}, user_id: {self.user_id}, custom_id: {self.custom_id}")

                # 检查token是否过期
                if self.expires_at and time.time() > self.expires_at:
                    logger.info("加载的token已过期，尝试刷新")
                    if not self.refresh_token():
                        logger.warning("刷新token失败，但保留custom_id以便使用X-User-ID认证")
                        self.token = None
                        self.refresh_token_str = None
                        # 保留user_id和custom_id，以便可以使用X-User-ID认证
                        self.expires_at = None
                else:
                    logger.info(f"已加载认证信息，用户ID: {self.user_id}, custom_id: {self.custom_id}")

                # 如果没有token但有custom_id，记录信息
                if not self.token and self.custom_id:
                    logger.info(f"没有有效token，但有custom_id: {self.custom_id}，可以使用X-User-ID认证")
            else:
                logger.info("认证信息文件不存在，未加载认证信息")
        except Exception as e:
            logger.error(f"加载认证信息失败: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())

    def _check_degraded_mode(self):
        """检查是否仍在降级模式"""
        if not self.degraded_mode:
            return False

        now = time.time()
        if now > self.degraded_mode_until:
            # 降级模式已过期，尝试恢复正常模式
            self.degraded_mode = False
            logger.info("降级模式已过期，恢复正常模式")
            return False

        return True

    def _switch_server(self, failed_server_index=None):
        """切换到下一个可用的服务器

        Args:
            failed_server_index: 失败的服务器索引，如果提供，将标记该服务器为不可用

        Returns:
            bool: 是否成功切换到新服务器
        """
        # 如果指定了失败的服务器，标记它
        if failed_server_index is not None:
            self.server_configs[failed_server_index]["failure_count"] += 1
            self.server_configs[failed_server_index]["last_check"] = time.time()

            # 如果连续失败次数超过阈值，标记为不可用
            if self.server_configs[failed_server_index]["failure_count"] >= 3:
                self.server_configs[failed_server_index]["available"] = False
                logger.warning(f"服务器 {self.server_configs[failed_server_index]['name']} 连续失败3次，标记为不可用")

        # 按优先级排序可用的服务器
        available_servers = [
            (i, server) for i, server in enumerate(self.server_configs)
            if server["available"] and i != self.current_server_index
        ]
        available_servers.sort(key=lambda x: x[1]["priority"])

        # 如果没有可用的服务器，返回失败
        if not available_servers:
            logger.error("没有可用的服务器")
            return False

        # 切换到优先级最高的可用服务器
        new_server_index, new_server = available_servers[0]
        self.current_server_index = new_server_index
        self.base_url = new_server["url"]
        self.current_server_name = new_server["name"]

        # 更新备用URL
        backup_servers = [
            server for i, server in enumerate(self.server_configs)
            if server["available"] and i != self.current_server_index
        ]
        if backup_servers:
            backup_servers.sort(key=lambda x: x["priority"])
            self.backup_url = backup_servers[0]["url"]
        else:
            self.backup_url = None

        logger.info(f"切换到服务器: {self.current_server_name} ({self.base_url})")
        return True

    def is_in_local_mode(self):
        """检查是否处于本地模式（降级模式）

        Returns:
            bool: 是否处于本地模式
        """
        return self._check_degraded_mode()

    def is_authenticated(self):
        """检查是否已认证，包括token认证和custom_id认证

        Returns:
            bool: 是否已认证
        """
        # 如果有token，则认为已认证
        if self.token is not None:
            return True

        # 如果没有token但有custom_id，也认为已认证（可以使用X-User-ID认证）
        if hasattr(self, 'custom_id') and self.custom_id:
            logger.info(f"没有token但有custom_id: {self.custom_id}，可以使用X-User-ID认证")
            return True

        # 尝试从用户管理器获取custom_id
        try:
            from utils.user_manager import get_user_manager
            user_manager = get_user_manager()
            current_user = user_manager.get_current_user()
            if current_user and hasattr(current_user, 'custom_id') and current_user.custom_id:
                # 更新实例的custom_id
                self.custom_id = current_user.custom_id
                logger.info(f"从用户管理器获取到custom_id: {self.custom_id}，可以使用X-User-ID认证")
                # 保存认证信息
                self.save_auth_info()
                return True
        except Exception as e:
            logger.error(f"尝试从用户管理器获取custom_id时出错: {str(e)}")

        return False

    def get_upload_queue_size(self):
        """获取上传队列大小

        Returns:
            int: 上传队列中的文件数量
        """
        try:
            if not os.path.exists(self.queue_dir):
                return 0

            # 获取队列中的文件数量
            files = [f for f in os.listdir(self.queue_dir) if os.path.isfile(os.path.join(self.queue_dir, f))]
            return len(files)
        except Exception as e:
            logger.error(f"获取上传队列大小时出错: {str(e)}")
            return 0

    def add_to_upload_queue(self, file_path, metadata=None):
        """将文件添加到上传队列，以便稍后上传

        Args:
            file_path (str): 文件路径
            metadata (dict, optional): 文件元数据

        Returns:
            bool: 是否成功添加到队列
        """
        try:
            # 确保队列目录存在
            if not os.path.exists(self.queue_dir):
                os.makedirs(self.queue_dir, exist_ok=True)
                logger.info(f"创建上传队列目录: {self.queue_dir}")

            # 检查文件是否存在
            if not os.path.exists(file_path):
                logger.error(f"文件不存在，无法添加到上传队列: {file_path}")
                return False

            # 生成队列文件名
            import uuid
            import time
            queue_id = str(uuid.uuid4())
            timestamp = int(time.time())
            file_name = os.path.basename(file_path)
            queue_file_name = f"{timestamp}_{queue_id}_{file_name}.json"
            queue_file_path = os.path.join(self.queue_dir, queue_file_name)

            # 准备队列项数据
            meta = metadata or {}

            # 确保元数据中包含用户ID
            if 'user_id' not in meta or not meta['user_id']:
                # 优先使用custom_id
                if hasattr(self, 'custom_id') and self.custom_id:
                    meta['user_id'] = self.custom_id
                    logger.info(f"在上传队列元数据中添加custom_id: {self.custom_id}")
                # 如果没有custom_id，尝试从用户管理器获取
                else:
                    try:
                        from utils.user_manager import get_user_manager
                        user_manager = get_user_manager()
                        custom_id = user_manager.get_current_user_custom_id()
                        if custom_id:
                            meta['user_id'] = custom_id
                            logger.info(f"在上传队列元数据中添加从用户管理器获取的custom_id: {custom_id}")
                    except Exception as e:
                        logger.error(f"尝试从用户管理器获取custom_id时出错: {str(e)}")

            queue_item = {
                "file_path": file_path,
                "metadata": meta,
                "created_at": timestamp,
                "status": "pending"
            }

            # 保存队列项
            with open(queue_file_path, 'w', encoding='utf-8') as f:
                json.dump(queue_item, f, ensure_ascii=False)

            logger.info(f"文件已添加到上传队列: {file_path}")
            return True

        except Exception as e:
            logger.error(f"添加文件到上传队列时出错: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return False

    def process_upload_queue(self, max_items=5):
        """处理上传队列

        Args:
            max_items (int): 最大处理项数

        Returns:
            tuple: (成功数, 失败数)
        """
        success_count = 0
        fail_count = 0

        try:
            # 确保队列目录存在
            if not os.path.exists(self.queue_dir):
                try:
                    os.makedirs(self.queue_dir, exist_ok=True)
                    logger.info(f"创建上传队列目录: {self.queue_dir}")
                except Exception as e:
                    logger.error(f"创建上传队列目录失败: {str(e)}")
                    return success_count, fail_count

            # 检查认证状态 - 如果没有token但有custom_id，也允许处理队列
            if not self.token and not hasattr(self, 'custom_id'):
                logger.warning("未认证且没有custom_id，暂不处理上传队列")
                return success_count, fail_count
            elif not self.token and hasattr(self, 'custom_id') and self.custom_id:
                logger.info(f"使用custom_id认证处理上传队列: {self.custom_id}")
                # 继续处理队列

            # 获取队列中的文件
            try:
                files = [f for f in os.listdir(self.queue_dir) if f.endswith('.json')]
            except Exception as e:
                logger.error(f"读取上传队列目录失败: {str(e)}")
                return success_count, fail_count

            if not files:
                logger.info("上传队列为空")
                return success_count, fail_count

            # 按创建时间排序
            try:
                files.sort(key=lambda x: os.path.getmtime(os.path.join(self.queue_dir, x)))
            except Exception as e:
                logger.warning(f"排序队列文件时出错: {str(e)}，将使用未排序列表")

            # 限制处理的最大数量
            files = files[:max_items]
            logger.info(f"开始处理上传队列，共 {len(files)} 个文件")

            for file_name in files:
                queue_file_path = os.path.join(self.queue_dir, file_name)
                try:
                    # 首先检查队列文件本身是否存在
                    if not os.path.exists(queue_file_path):
                        logger.error(f"队列文件不存在: {queue_file_path}")
                        fail_count += 1
                        continue

                    # 读取队列项
                    try:
                        with open(queue_file_path, 'r', encoding='utf-8') as f:
                            queue_item = json.load(f)
                    except json.JSONDecodeError as je:
                        logger.error(f"队列文件格式错误: {queue_file_path}, 错误: {str(je)}")
                        # 尝试删除损坏的队列文件
                        try:
                            os.remove(queue_file_path)
                            logger.info(f"已删除损坏的队列文件: {queue_file_path}")
                        except Exception as del_err:
                            logger.warning(f"无法删除损坏的队列文件: {queue_file_path}, 错误: {str(del_err)}")
                        fail_count += 1
                        continue

                    # 检查队列项是否包含必要的字段
                    if not isinstance(queue_item, dict):
                        logger.error(f"队列项格式错误，不是有效的字典: {queue_file_path}")
                        try:
                            os.remove(queue_file_path)
                            logger.info(f"已删除格式错误的队列文件: {queue_file_path}")
                        except Exception as del_err:
                            logger.warning(f"无法删除队列文件: {queue_file_path}, 错误: {str(del_err)}")
                        fail_count += 1
                        continue

                    file_path = queue_item.get("file_path")
                    metadata = queue_item.get("metadata", {})

                    # 检查文件路径是否有效
                    if not file_path:
                        logger.error(f"队列项缺少文件路径: {queue_file_path}")
                        try:
                            os.remove(queue_file_path)
                            logger.info(f"已删除缺少文件路径的队列项: {queue_file_path}")
                        except Exception as del_err:
                            logger.warning(f"无法删除队列文件: {queue_file_path}, 错误: {str(del_err)}")
                        fail_count += 1
                        continue

                    # 检查文件是否存在
                    if not os.path.exists(file_path):
                        logger.error(f"队列中的文件不存在: {file_path}")
                        try:
                            os.remove(queue_file_path)  # 删除无效的队列项
                            logger.info(f"已删除指向不存在文件的队列项: {queue_file_path}")
                        except Exception as del_err:
                            logger.warning(f"无法删除队列文件: {queue_file_path}, 错误: {str(del_err)}")
                        fail_count += 1
                        continue

                    # 检查文件是否可读
                    try:
                        with open(file_path, 'rb') as f:
                            # 只读取一小部分确认文件可访问
                            f.read(1024)
                    except Exception as e:
                        logger.error(f"队列中的文件无法读取: {file_path}, 错误: {str(e)}")
                        try:
                            os.remove(queue_file_path)  # 删除无效的队列项
                            logger.info(f"已删除指向无法读取文件的队列项: {queue_file_path}")
                        except Exception as del_err:
                            logger.warning(f"无法删除队列文件: {queue_file_path}, 错误: {str(del_err)}")
                        fail_count += 1
                        continue

                    # 上传文件
                    logger.info(f"从队列中上传文件: {file_path}")
                    result = self.upload_file(file_path, metadata)

                    if result:
                        logger.info(f"队列文件上传成功: {file_path}")
                        try:
                            os.remove(queue_file_path)  # 删除队列项
                            logger.info(f"已删除成功处理的队列项: {queue_file_path}")
                        except Exception as del_err:
                            logger.warning(f"无法删除队列文件: {queue_file_path}, 错误: {str(del_err)}")
                        success_count += 1
                    else:
                        logger.error(f"队列文件上传失败: {file_path}, 错误: {self.last_error if hasattr(self, 'last_error') else '未知错误'}")
                        # 检查是否需要重试
                        retry_count = queue_item.get("retry_count", 0) + 1
                        if retry_count <= 3:  # 最多重试3次
                            queue_item["retry_count"] = retry_count
                            # 使用指数退避策略
                            next_retry = int(time.time()) + (2 ** retry_count) * 60  # 2^重试次数 分钟后重试
                            queue_item["next_retry"] = next_retry
                            try:
                                with open(queue_file_path, 'w', encoding='utf-8') as f:
                                    json.dump(queue_item, f, ensure_ascii=False)
                                logger.info(f"队列项已更新重试信息，将在 {(next_retry - int(time.time())) / 60:.1f} 分钟后重试: {file_path}")
                            except Exception as e:
                                logger.error(f"更新队列项重试信息失败: {str(e)}")
                                fail_count += 1
                        else:
                            logger.warning(f"队列文件 {file_path} 已达到最大重试次数，放弃处理")
                            try:
                                os.remove(queue_file_path)  # 删除失败的队列项
                                logger.info(f"已删除达到最大重试次数的队列项: {queue_file_path}")
                            except Exception as del_err:
                                logger.warning(f"无法删除队列文件: {queue_file_path}, 错误: {str(del_err)}")
                            fail_count += 1
                except Exception as e:
                    logger.error(f"处理队列文件 {file_name} 失败: {str(e)}")
                    import traceback
                    logger.error(traceback.format_exc())
                    fail_count += 1
        except Exception as e:
            logger.error(f"处理上传队列时出错: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())

        logger.info(f"上传队列处理完成: {success_count} 个成功, {fail_count} 个失败")
        return success_count, fail_count

    def check_server_health(self):
        """检查服务器健康状态，并自动选择最佳服务器

        Returns:
            dict: 包含服务器健康状态信息
        """
        # 如果最近进行过健康检查且在有效期内，直接返回缓存的结果
        now = time.time()
        if hasattr(self, 'last_health_check') and self.last_health_check:
            last_check_time = self.last_health_check.get('timestamp', 0)
            if now - last_check_time <= 60:  # 1分钟内的健康检查结果有效
                return self.last_health_check

        # 初始化健康检查结果
        health_result = {
            'online': False,
            'check_time': now,  # 使用check_time替代timestamp
            'message': '',
            'servers': []
        }

        # 检查所有服务器的健康状态
        any_server_online = False
        for i, server in enumerate(self.server_configs):
            # 如果最近检查过且时间不超过5分钟，跳过检查
            if now - server["last_check"] < 300 and not server["available"]:
                health_result['servers'].append({
                    'name': server['name'],
                    'url': server['url'],
                    'online': False,
                    'message': '服务器不可用'
                })
                continue

            server_health = {
                'name': server['name'],
                'url': server['url'],
                'online': False,
                'message': ''
            }

            try:
                # 尝试连接服务器健康检查端点
                import requests
                logger.info(f"检查服务器健康状态: {server['name']} ({server['url']})")

                # 尝试不同的健康检查端点
                endpoints = [
                    "/api/health/ping",
                    "/api/health",
                    "/api/health/check",
                    "/api/ping"
                ]

                server_is_online = False
                for endpoint in endpoints:
                    try:
                        health_url = f"{server['url']}{endpoint}"
                        logger.info(f"尝试健康检查端点: {health_url}")
                        response = requests.get(
                            health_url,
                            timeout=5,
                            proxies={
                                'http': None,
                                'https': None
                            }
                        )

                        if response.status_code == 200:
                            # 服务器在线
                            server_health['online'] = True
                            server_health['message'] = f'服务器连接正常 (通过{endpoint})'
                            server_is_online = True
                            any_server_online = True

                            # 成功连接后，不需要尝试其他端点
                            break
                    except Exception as e:
                        logger.warning(f"健康检查端点 {endpoint} 失败: {str(e)}")
                        continue

                # 如果所有健康检查端点都失败，但服务器URL可能仍然有效，尝试直接访问根URL
                if not server_is_online:
                    try:
                        root_url = server['url']
                        logger.info(f"尝试访问根URL: {root_url}")
                        response = requests.get(root_url, timeout=5)

                        if response.status_code < 500:  # 任何非5xx响应都表示服务器在线
                            server_health['online'] = True
                            server_health['message'] = '服务器根URL连接正常'
                            server_is_online = True
                            any_server_online = True
                    except Exception as e:
                        logger.warning(f"访问根URL失败: {str(e)}")

                # 根据检查结果更新服务器状态
                if server_is_online:
                    # 重置失败计数
                    server["failure_count"] = 0
                    server["available"] = True
                else:
                    # 服务器所有检查点都失败
                    server_health['message'] = '无法连接服务器的任何健康检查端点'
                    server["failure_count"] += 1
            except Exception as e:
                # 连接失败
                server_health['message'] = f'无法连接服务器: {str(e)}'
                server["failure_count"] += 1
                logger.warning(f"检查服务器 {server['name']} 健康状态时出错: {str(e)}")

            # 更新服务器状态
            server["last_check"] = now
            if server["failure_count"] >= 3:
                server["available"] = False
                logger.warning(f"服务器 {server['name']} 连续失败3次，标记为不可用")

            health_result['servers'].append(server_health)

        # 设置整体健康状态
        health_result['online'] = any_server_online
        if any_server_online:
            health_result['message'] = '至少有一个服务器在线'
        else:
            health_result['message'] = '所有服务器都离线'

        # 缓存健康检查结果
        self.last_health_check = health_result

        # 根据健康检查结果设置降级模式
        if not any_server_online:
            if not self.degraded_mode:
                logger.warning("所有服务器都离线，进入降级模式")
                self.degraded_mode = True
                # 使用相对时间而不是绝对时间戳
                self.degraded_mode_duration = 300  # 5分钟后自动恢复
        else:
            if self.degraded_mode:
                logger.info("检测到服务器在线，退出降级模式")
                self.degraded_mode = False

        # 如果当前服务器不可用，尝试切换到可用服务器
        if not self.server_configs[self.current_server_index]["available"]:
            old_server_name = self.current_server_name
            if self._switch_server():
                logger.info(f"当前服务器不可用，已从 {old_server_name} 切换到 {self.current_server_name}")

        return health_result

    def register_user(self, user_data):
        """注册新用户 - 修改后直接使用后端API

        Args:
            user_data (dict): 用户数据

        Returns:
            dict: 注册结果
        """
        # 导入API适配器
        from .api_adapters import adapt_register_data

        # 记录原始用户数据（不包含密码）
        log_data = user_data.copy()
        if "password" in log_data:
            log_data["password"] = "******"  # 隐藏密码
        logger.info(f"原始注册数据: {log_data}")

        # 适配数据格式为后端API期望的格式
        api_data = adapt_register_data(user_data)
        if not api_data:
            logger.error("无法适配用户数据为后端API格式")
            self.last_error = "数据格式不兼容后端API要求"
            return None

        # 记录适配后的数据（不包含密码）
        log_api_data = api_data.copy()
        if "password" in log_api_data:
            log_api_data["password"] = "******"  # 隐藏密码
        logger.info(f"适配后的注册数据: {log_api_data}")

        # 检查必要字段
        required_fields = ["username", "password", "email"]
        for field in required_fields:
            if field not in api_data:
                logger.error(f"注册用户缺少必要字段: {field}")
                self.last_error = f"缺少必要字段: {field}"
                return None

        # 确保角色字段正确
        if "role" not in api_data or not api_data["role"]:
            # 如果没有设置角色，默认为个人用户
            api_data["role"] = "personal"
            logger.info(f"未设置角色，使用默认角色: personal")
        else:
            logger.info(f"使用角色: {api_data['role']}")

        # 确保additional_roles是字符串列表
        if "additional_roles" in api_data:
            if isinstance(api_data["additional_roles"], list):
                # 确保列表中的所有元素都是字符串
                api_data["additional_roles"] = [str(role) for role in api_data["additional_roles"]]
            elif api_data["additional_roles"] is not None:
                # 如果不是列表但有值，转换为列表
                api_data["additional_roles"] = [str(api_data["additional_roles"])]
            logger.info(f"附加角色: {api_data['additional_roles']}")

        # 调用后端API注册用户
        logger.info(f"向后端发送注册请求: {api_data.get('username')}")

        # 使用移动端兼容的注册端点
        result = self._make_request(
            method="POST",
            endpoint="auth/register",  # 使用正确的注册端点
            json_data=api_data,
            bypass_auth=True,
            max_retries=3,  # 重试次数
            timeout=30      # 超时时间
        )

        # 处理响应
        if result:
            # 检查注册是否成功
            if result.get("status") == "success":
                logger.info(f"用户注册成功: {api_data.get('username')}")

                # 如果后端返回了用户ID，保存它
                if "user_id" in result:
                    user_data["user_id"] = result["user_id"]

                # 如果后端返回了访问令牌，保存它
                if "access_token" in result:
                    self.token = result["access_token"]
                    self.save_auth_info()

                return result
            else:
                # 注册失败，提取错误信息
                error_message = result.get("message", "未知错误")
                logger.error(f"用户注册失败: {error_message}")
                self.last_error = error_message
                return None
        else:
            # 请求失败
            error_code = 0
            retry_delay = 60  # 默认1分钟后重试

            # 解析错误类型
            if self.last_error:
                # 处理502网关错误
                if "502" in self.last_error:
                    error_code = 502
                    retry_delay = 120  # 2分钟后重试
                    logger.error(f"后端注册失败: 502网关错误，服务器暂时不可用")
                # 处理其他5xx服务器错误
                elif any(str(code) in self.last_error for code in range(500, 600)):
                    error_code = 500
                    retry_delay = 180  # 3分钟后重试
                    logger.error(f"后端注册失败: 服务器错误")
                # 处理网络连接错误
                elif "请求异常" in self.last_error:
                    error_code = -1
                    retry_delay = 60  # 1分钟后重试
                    logger.error(f"后端注册失败: 网络连接问题")

            # 返回一个包含错误信息的结果
            return {
                "success": False,
                "message": f"注册失败: {self.last_error}",
                "error_code": error_code
            }

        return None

    def get_user_info(self):
        """获取当前登录用户的信息

        Returns:
            dict: 用户信息，失败时返回None
        """
        if not self.token:
            logger.error("未登录，无法获取用户信息")
            return None

        result = self._make_request(
            method="GET",
            endpoint="users/me"
        )

        if result and (result.get("success") or result.get("status") == "success"):
            # 获取用户数据，兼容不同的响应格式
            user_info = result.get("data", {})
            if not user_info and isinstance(result, dict):
                # 如果没有data字段，但result本身是字典，可能整个result就是数据
                user_info = result

            # 确保有user_id字段
            if "user_id" not in user_info and "id" in user_info:
                user_info["user_id"] = user_info["id"]

            # 检查是否有custom_id字段
            if "custom_id" in user_info and user_info["custom_id"]:
                logger.info(f"用户信息包含custom_id: {user_info['custom_id']}")
            else:
                logger.warning("用户信息中没有custom_id，这可能导致某些功能无法正常工作")

            return user_info

        return None

    def upload_file(self, file_path, metadata=None, file_type=None, document_type=None, description=None):
        """上传文件到后端服务器 - 修改后与后端API兼容

        Args:
            file_path (str): 文件路径
            metadata (dict, optional): 文件元数据
            file_type (str, optional): 文件类型
            document_type (str, optional): 文档类型
            description (str, optional): 文件描述

        Returns:
            dict: 上传结果，失败时返回None
        """
        # 导入API适配器
        from .api_adapters import adapt_document_upload_data

        # 首先检查用户是否已登录
        from utils.user_manager import get_user_manager
        user_manager = get_user_manager()
        current_user = user_manager.get_current_user()

        # 检查认证状态
        is_auth = self.is_authenticated()
        logger.info(f"上传文件前的认证状态: {is_auth}")

        # 如果未认证但有current_user，尝试设置认证信息
        if not is_auth and current_user:
            if hasattr(current_user, 'custom_id') and current_user.custom_id:
                self.custom_id = current_user.custom_id
                logger.info(f"从current_user获取custom_id: {current_user.custom_id}")
                # 保存认证信息
                self.save_auth_info()
                # 重新检查认证状态
                is_auth = self.is_authenticated()
                logger.info(f"设置custom_id后的认证状态: {is_auth}")

        # 如果仍然未认证，添加到上传队列
        if not is_auth:
            logger.warning("用户未登录，将文件添加到上传队列，等待用户登录后再上传")
            queued = self.add_to_upload_queue(file_path, metadata or {})
            if queued:
                logger.info(f"文件已添加到上传队列: {file_path}")
                self.last_error = "用户未登录，文件已添加到上传队列"
            else:
                logger.error(f"添加文件到上传队列失败: {file_path}")
                self.last_error = "用户未登录，且添加到上传队列失败"
            return None

        # 检查认证状态
        if not self.token:
            # 尝试加载认证信息
            self.load_auth_info()

            # 再次检查认证状态
            if not self.token:
                # 检查是否有custom_id，如果有则可以继续上传
                if current_user and hasattr(current_user, 'custom_id') and current_user.custom_id:
                    # 使用用户管理器中的custom_id
                    self.custom_id = current_user.custom_id
                    logger.warning(f"虽然没有token，但有custom_id: {self.custom_id}，继续上传文件")

                    # 直接使用custom_id进行上传，不需要添加到队列
                    logger.info(f"使用custom_id: {self.custom_id}直接上传文件")
                    # 继续执行上传逻辑，不返回None

                    # 保存认证信息，确保下次可以直接使用
                    self.save_auth_info()
                elif hasattr(self, 'custom_id') and self.custom_id:
                    logger.warning(f"虽然没有token，但有custom_id: {self.custom_id}，继续上传文件")
                    # 继续执行上传逻辑，不返回None
                else:
                    logger.error("未登录，无法上传文件")
                    self.last_error = "未登录，无法上传文件"

                    # 记录详细的认证状态信息，便于调试
                    logger.debug(f"认证状态: token={bool(self.token)}, user_id={self.user_id}, custom_id={getattr(self, 'custom_id', None)}, refresh_token={bool(self.refresh_token_str)}")

                    # 添加到上传队列，等待用户登录后再上传
                    logger.info("将文件添加到上传队列，等待用户登录后再上传")
                    queued = self.add_to_upload_queue(file_path, metadata or {})
                    if queued:
                        logger.info(f"文件已添加到上传队列: {file_path}")
                        self.last_error = "未登录，文件已添加到上传队列"
                    else:
                        logger.error(f"添加文件到上传队列失败: {file_path}")
                        self.last_error = "未登录，且添加到上传队列失败"
                    return None

        # 尝试刷新令牌（如果没有token但有refresh_token）
        if not self.token and self.refresh_token_str:
            logger.info("尝试刷新令牌...")
            if self.refresh_token():
                logger.info("令牌刷新成功，继续上传文件")
            else:
                logger.error("令牌刷新失败")
                # 检查是否是本地服务器模式
                if "localhost" in self.base_url or "127.0.0.1" in self.base_url:
                    logger.info("检测到本地服务器模式，将使用本地上传队列")
                    # 添加到上传队列，稍后再尝试
                    queued = self.add_to_upload_queue(file_path, metadata or {})
                    if queued:
                        logger.info(f"文件已添加到上传队列: {file_path}")
                        self.last_error = "本地服务器需要认证，文件已添加到上传队列"
                    else:
                        logger.error(f"添加文件到上传队列失败: {file_path}")
                        self.last_error = "本地服务器需要认证，且添加到上传队列失败"
                return None

        # 尝试从用户管理器获取custom_id
        try:
            from utils.user_manager import get_user_manager
            user_manager = get_user_manager()
            custom_id = user_manager.get_current_user_custom_id()
            if custom_id:
                logger.info(f"从用户管理器获取到custom_id: {custom_id}，继续上传文件")
                self.custom_id = custom_id
                # 继续执行上传逻辑
            else:
                logger.warning("无法从用户管理器获取custom_id")
                # 尝试刷新令牌
                if self.refresh_token_str:
                    logger.info("尝试刷新令牌...")
                    if self.refresh_token():
                        logger.info("令牌刷新成功，继续上传文件")
                    else:
                        logger.error("令牌刷新失败")
                        self.last_error = "未登录且无法获取custom_id，无法上传文件"
                        return None
                else:
                    logger.error("无法获取custom_id，无法上传文件")
                    self.last_error = "未登录且无法获取custom_id，无法上传文件"
                    return None
        except Exception as e:
            logger.error(f"尝试从用户管理器获取custom_id时出错: {str(e)}")
            # 尝试刷新令牌
            if self.refresh_token_str:
                logger.info("尝试刷新令牌...")
                if self.refresh_token():
                    logger.info("令牌刷新成功，继续上传文件")
                else:
                    logger.error("令牌刷新失败")
                    self.last_error = "未登录且无法获取custom_id，无法上传文件"
                    return None
            else:
                logger.error("无法获取custom_id，无法上传文件")
                self.last_error = "未登录且无法获取custom_id，无法上传文件"
                return None

        if not os.path.exists(file_path):
            logger.error(f"文件不存在: {file_path}")
            self.last_error = f"文件不存在: {file_path}"
            return None

        try:
            # 准备文件对象
            with open(file_path, 'rb') as f:
                file_content = f.read()

            file_name = os.path.basename(file_path)
            logger.info(f"准备上传文件: {file_name}, 大小: {len(file_content)} 字节")

            # 创建文件对象
            from types import SimpleNamespace
            file_obj = SimpleNamespace()
            file_obj.name = file_name
            file_obj.content_type = self._get_mime_type(file_path)

            # 准备元数据
            meta = metadata or {}
            if file_type and 'file_type' not in meta:
                meta['file_type'] = file_type
            if document_type and 'document_type' not in meta:
                meta['document_type'] = document_type
            if description and 'description' not in meta:
                meta['description'] = description

            # 确保元数据中包含用户ID
            if 'user_id' not in meta or not meta['user_id']:
                # 优先使用custom_id
                if hasattr(self, 'custom_id') and self.custom_id:
                    meta['user_id'] = self.custom_id
                    logger.info(f"在元数据中添加custom_id: {self.custom_id}")
                # 如果没有custom_id，尝试从用户管理器获取
                else:
                    try:
                        from utils.user_manager import get_user_manager
                        user_manager = get_user_manager()
                        custom_id = user_manager.get_current_user_custom_id()
                        if custom_id:
                            meta['user_id'] = custom_id
                            logger.info(f"在元数据中添加从用户管理器获取的custom_id: {custom_id}")
                    except Exception as e:
                        logger.error(f"尝试从用户管理器获取custom_id时出错: {str(e)}")

            # 添加来源标记
            meta['source'] = 'mobile'

            # 添加创建时间（使用ISO格式而不是时间戳）
            if 'created_at' not in meta:
                from datetime import datetime
                meta['created_at'] = datetime.now().isoformat()

            # 添加用户ID信息 - 优先使用custom_id
            if 'user_id' not in meta and 'custom_id' not in meta:
                # 如果元数据中没有用户ID，添加当前登录用户的ID
                if hasattr(self, 'custom_id') and self.custom_id:
                    meta['custom_id'] = self.custom_id
                    logger.info(f"添加当前用户custom_id到元数据: {self.custom_id}")
                elif self.user_id:
                    meta['user_id'] = self.user_id
                    logger.info(f"添加当前用户user_id到元数据: {self.user_id}")

            # 记录完整的元数据
            logger.info(f"完整的文件上传元数据: {meta}")

            # 适配数据格式
            form_data, files = adapt_document_upload_data(meta, file_obj)

            # 替换文件内容
            files['file'] = (file_name, file_content, file_obj.content_type)

            # 准备请求头
            headers = {}

            # 如果有token，添加认证头
            if self.token:
                # 使用辅助方法确保令牌格式正确
                if self._ensure_token_format():
                    # 令牌格式正确或已成功修复
                    headers["Authorization"] = f"Bearer {self.token}"
                    logger.info(f"使用token进行认证: {self.token[:10]}...")
                else:
                    # 令牌格式无法修复
                    logger.warning("令牌格式无法修复，不使用Bearer认证")
                    # 如果令牌无法修复，不添加Authorization头，将依赖X-User-ID认证
            else:
                logger.info("没有token，将依赖X-User-ID认证")

            # 无论是否有token，只要有custom_id，都添加X-User-ID头
            if hasattr(self, 'custom_id') and self.custom_id:
                headers["X-User-ID"] = self.custom_id
                logger.info(f"添加X-User-ID头: {self.custom_id}")
            else:
                logger.warning("没有custom_id，无法添加X-User-ID头，上传可能失败")

            # 发送上传请求
            logger.info(f"发送文件上传请求: {file_name}, 元数据: {meta}")

            # 确保使用正确的端点路径，避免双重api路径
            # 尝试使用移动端专用上传接口
            endpoint = "documents/mobile-upload"

            # 如果移动端专用接口不可用，回退到标准接口
            standard_endpoint = "documents/upload"

            # 检查当前服务器URL是否已包含/api前缀
            if "/api" in self.base_url:
                logger.info(f"base_url已包含/api前缀: {self.base_url}")
                # 确保endpoint不以api/开头
                if endpoint.startswith("api/"):
                    endpoint = endpoint[4:]
                    logger.info(f"移除endpoint中的api/前缀，修正为: {endpoint}")
                if standard_endpoint.startswith("api/"):
                    standard_endpoint = standard_endpoint[4:]

            # 记录完整的请求信息，便于调试
            logger.info(f"上传请求详情 - 方法: POST, 端点: {endpoint}, 认证头: {bool(headers.get('Authorization'))}, X-User-ID: {headers.get('X-User-ID')}, 服务器: {self.base_url}")

            # 尝试发送请求到移动端专用接口
            result = self._make_request(
                method="POST",
                endpoint=endpoint,
                files=files,
                data=form_data,
                headers=headers
            )

            # 如果移动端专用接口返回404，尝试使用标准接口
            if not result or (isinstance(result, dict) and result.get("status_code") == 404):
                logger.info(f"移动端专用上传接口不可用（404），尝试使用标准接口: {standard_endpoint}")

                # 尝试标准接口
                result = self._make_request(
                    method="POST",
                    endpoint=standard_endpoint,
                    files=files,
                    data=form_data,
                    headers=headers
                )

            if result:
                # 检查不同的成功响应格式
                if result.get("success") or result.get("status") == "success" or result.get("id"):
                    logger.info(f"文件上传成功: {file_name}")

                    # 提取数据，兼容不同的响应格式
                    data = result.get("data", {})
                    if not data and isinstance(result, dict):
                        # 如果没有data字段，但result本身是字典，可能整个result就是数据
                        data = result

                    # 确保有文件ID
                    if "file_id" not in data and "id" in data:
                        data["file_id"] = data["id"]
                    elif "file_id" not in data and "id" in result:
                        data["file_id"] = result["id"]

                    # 记录详细的响应信息
                    logger.info(f"文件上传响应详情: {data}")

                    return data
            else:
                error_msg = "上传请求失败，无法连接服务器"
                logger.error(f"文件上传失败: {error_msg}")
                self.last_error = error_msg
                return None

        except Exception as e:
            error_msg = f"上传文件时出错: {str(e)}"
            logger.error(error_msg)
            import traceback
            logger.error(traceback.format_exc())
            self.last_error = error_msg
            return None

    def _ensure_token_format(self):
        """确保令牌格式正确，如果不正确则尝试修复

        Returns:
            bool: 令牌格式是否正确或已成功修复
        """
        if not self.token:
            logger.warning("令牌为空，无法检查格式")
            return False

        # 清理令牌格式
        clean_token = self.token.strip().replace(" ", "").replace("\n", "").replace("\r", "").replace("\t", "")

        # 检查令牌格式
        token_parts = clean_token.split('.')
        if len(token_parts) != 3:
            logger.warning(f"令牌格式异常，部分数量: {len(token_parts)}")

            # 尝试修复令牌格式问题
            import re
            jwt_pattern = r'(eyJ[a-zA-Z0-9_-]+)\.(eyJ[a-zA-Z0-9_-]+)\.([a-zA-Z0-9_-]+)'
            matches = re.search(jwt_pattern, clean_token)
            if matches:
                header, payload, signature = matches.groups()
                clean_token = f"{header}.{payload}.{signature}"
                logger.info(f"令牌格式已修复为标准格式")
                # 更新实例中的token
                self.token = clean_token
                # 保存修复后的token
                self.save_auth_info()
                return True
            else:
                logger.warning(f"无法修复令牌格式")
                return False
        else:
            # 令牌格式正确
            if clean_token != self.token:
                # 如果令牌有变化，更新实例中的token
                self.token = clean_token
                self.save_auth_info()
                logger.info("令牌格式已清理")
            return True

    def _get_mime_type(self, file_path):
        """获取文件MIME类型

        Args:
            file_path (str): 文件路径

        Returns:
            str: MIME类型
        """
        import mimetypes
        mime_type, _ = mimetypes.guess_type(file_path)
        return mime_type or 'application/octet-stream'

    def _save_auth_info(self, token, user_id):
        """保存认证信息到本地存储

        Args:
            token: 访问令牌
            user_id: 用户ID
        """
        try:
            # 创建保存目录
            data_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data')
            os.makedirs(data_dir, exist_ok=True)

            # 检查custom_id是否已设置
            if not hasattr(self, 'custom_id') or not self.custom_id:
                logger.warning("没有custom_id，这可能导致某些功能无法正常工作")

                # 尝试从用户管理器获取custom_id
                try:
                    from utils.user_manager import get_user_manager
                    user_manager = get_user_manager()
                    stored_custom_id = user_manager.get_current_user_custom_id()
                    if stored_custom_id:
                        self.custom_id = stored_custom_id
                        logger.info(f"从用户管理器获取到custom_id: {self.custom_id}")
                except Exception as e:
                    logger.warning(f"尝试从用户管理器获取custom_id时出错: {str(e)}")

            # 保存令牌到本地文件
            auth_data = {
                "token": token,
                "user_id": user_id,
                "custom_id": self.custom_id,  # 添加custom_id字段
                "refresh_token": self.refresh_token_str,
                "expires_at": self.expires_at,
                "timestamp": time.time()
            }

            # 保存到文件
            token_file = os.path.join(data_dir, 'cloud_auth.json')
            with open(token_file, 'w', encoding='utf-8') as f:
                json.dump(auth_data, f, ensure_ascii=False)

            # 更新实例变量
            self.token = token
            self.user_id = user_id

            logger.info(f"已保存认证信息到本地存储，用户ID: {user_id}, custom_id: {self.custom_id}")
        except Exception as e:
            logger.error(f"保存认证信息失败: {str(e)}")
