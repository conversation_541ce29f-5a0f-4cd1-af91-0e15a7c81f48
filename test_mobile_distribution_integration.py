#!/usr/bin/env python3
"""
完整测试移动端分发通知集成功能
"""

import json
import logging
import sys
import os
from datetime import datetime, timedelta

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_cloud_api_integration():
    """测试云端API集成"""
    print("=== 测试云端API集成 ===")
    
    try:
        from utils.cloud_api import get_cloud_api
        cloud_api = get_cloud_api()
        
        # 检查新的移动端API方法
        methods_to_check = [
            'get_mobile_assessments',
            'get_mobile_questionnaires', 
            'submit_mobile_assessment',
            'submit_mobile_questionnaire'
        ]
        
        for method_name in methods_to_check:
            if hasattr(cloud_api, method_name):
                print(f"✓ {method_name} 方法已添加")
            else:
                print(f"✗ {method_name} 方法缺失")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ 云端API集成测试失败: {e}")
        return False

def test_distribution_manager():
    """测试分发管理器"""
    print("\n=== 测试分发管理器 ===")
    
    try:
        from utils.distribution_manager import get_distribution_manager
        distribution_manager = get_distribution_manager()
        
        # 测试评估量表分发通知
        assessment_notification = create_test_assessment_notification()
        success = distribution_manager.process_distribution_notification(assessment_notification)
        
        if success:
            print("✓ 评估量表分发通知处理成功")
        else:
            print("✗ 评估量表分发通知处理失败")
            return False
        
        # 测试问卷分发通知
        questionnaire_notification = create_test_questionnaire_notification()
        success = distribution_manager.process_distribution_notification(questionnaire_notification)
        
        if success:
            print("✓ 问卷分发通知处理成功")
        else:
            print("✗ 问卷分发通知处理失败")
            return False
        
        # 测试获取待完成项目
        pending_assessments = distribution_manager.get_pending_assessments()
        pending_questionnaires = distribution_manager.get_pending_questionnaires()
        
        print(f"✓ 获取到 {len(pending_assessments)} 个待完成评估量表")
        print(f"✓ 获取到 {len(pending_questionnaires)} 个待完成问卷")
        
        return True
        
    except Exception as e:
        print(f"✗ 分发管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_push_notification_handler():
    """测试推送通知处理器"""
    print("\n=== 测试推送通知处理器 ===")
    
    try:
        from utils.push_notification_handler import get_push_notification_handler, get_mock_push_service
        
        # 获取推送通知处理器
        handler = get_push_notification_handler()
        
        # 注册测试回调
        received_notifications = []
        
        def test_callback(notification_data):
            received_notifications.append(notification_data)
            print(f"  收到通知: {notification_data.get('title')}")
        
        handler.register_callback('distribution', test_callback)
        
        # 获取模拟推送服务
        mock_service = get_mock_push_service()
        
        # 连接服务
        if not mock_service.connect():
            print("✗ 模拟推送服务连接失败")
            return False
        
        print("✓ 模拟推送服务连接成功")
        
        # 模拟分发通知
        assessment_data = create_test_assessment_distribution_data()
        success = mock_service.simulate_distribution_notification('assessment', assessment_data)
        
        if success:
            print("✓ 模拟评估量表分发通知成功")
        else:
            print("✗ 模拟评估量表分发通知失败")
            return False
        
        # 检查回调是否被调用
        if len(received_notifications) > 0:
            print("✓ 分发通知回调被正确调用")
        else:
            print("✗ 分发通知回调未被调用")
            return False
        
        # 断开服务
        mock_service.disconnect()
        print("✓ 模拟推送服务断开成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 推送通知处理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_test_assessment_notification():
    """创建测试评估量表分发通知"""
    return {
        "id": "test_assessment_dist_001",
        "type": "distribution",
        "content_type": "assessment",
        "title": "心理健康评估量表分发",
        "message": "您有一个新的心理健康评估量表需要完成",
        "distribution_data": create_test_assessment_distribution_data(),
        "created_at": datetime.now().isoformat() + "Z",
        "expires_at": (datetime.now() + timedelta(days=7)).isoformat() + "Z",
        "priority": "high",
        "read_status": False
    }

def create_test_assessment_distribution_data():
    """创建测试评估量表分发数据"""
    return {
        "distribution_id": 12345,
        "template_id": 1,
        "template_key": "hamilton_depression",
        "assessment_info": {
            "name": "汉密尔顿抑郁量表",
            "name_en": "Hamilton Depression Rating Scale",
            "description": "用于评估抑郁症状严重程度的专业量表",
            "category": "心理健康",
            "assessment_type": "depression",
            "sub_type": "clinical",
            "version": "1.0",
            "max_score": 52,
            "estimated_time": 15
        },
        "questions": [
            {
                "id": 1,
                "text": "抑郁情绪（悲伤、绝望、无助、无价值感）",
                "type": "single_choice",
                "options": [
                    {"value": 0, "text": "无"},
                    {"value": 1, "text": "轻度"},
                    {"value": 2, "text": "中度"},
                    {"value": 3, "text": "重度"},
                    {"value": 4, "text": "极重度"}
                ],
                "required": True
            },
            {
                "id": 2,
                "text": "感到悲伤和沮丧",
                "type": "single_choice",
                "options": [
                    {"value": 0, "text": "无"},
                    {"value": 1, "text": "轻度"},
                    {"value": 2, "text": "中度"},
                    {"value": 3, "text": "重度"},
                    {"value": 4, "text": "极重度"}
                ],
                "required": True
            }
        ],
        "instructions": "请根据您最近一周的感受选择最符合的选项",
        "scoring_method": "sum",
        "result_ranges": [
            {"min": 0, "max": 7, "level": "正常", "description": "无抑郁症状"},
            {"min": 8, "max": 16, "level": "轻度", "description": "轻度抑郁"},
            {"min": 17, "max": 23, "level": "中度", "description": "中度抑郁"},
            {"min": 24, "max": 52, "level": "重度", "description": "重度抑郁"}
        ],
        "due_date": (datetime.now() + timedelta(days=7)).isoformat() + "Z",
        "distributor": {
            "id": 1,
            "name": "张医生",
            "role": "doctor"
        }
    }

def create_test_questionnaire_notification():
    """创建测试问卷分发通知"""
    return {
        "id": "test_questionnaire_dist_001",
        "type": "distribution",
        "content_type": "questionnaire",
        "title": "健康生活方式调查问卷分发",
        "message": "请完成健康生活方式调查问卷",
        "distribution_data": {
            "distribution_id": 67890,
            "questionnaire_id": 2,
            "questionnaire_info": {
                "title": "健康生活方式调查",
                "description": "了解您的日常生活习惯和健康状况",
                "category": "生活方式",
                "questionnaire_type": "survey",
                "assessment_type": "self",
                "estimated_time": 10
            },
            "questions": [
                {
                    "id": 1,
                    "text": "您每天的睡眠时间是多少？",
                    "type": "single_choice",
                    "options": [
                        {"value": "less_than_6", "text": "少于6小时"},
                        {"value": "6_to_8", "text": "6-8小时"},
                        {"value": "more_than_8", "text": "超过8小时"}
                    ],
                    "required": True
                },
                {
                    "id": 2,
                    "text": "请描述您的运动习惯",
                    "type": "text",
                    "required": False,
                    "max_length": 500
                }
            ],
            "instructions": "请根据您的实际情况如实填写",
            "due_date": (datetime.now() + timedelta(days=7)).isoformat() + "Z",
            "distributor": {
                "id": 1,
                "name": "健康管理员",
                "role": "health_manager"
            }
        },
        "created_at": datetime.now().isoformat() + "Z",
        "expires_at": (datetime.now() + timedelta(days=7)).isoformat() + "Z",
        "priority": "medium",
        "read_status": False
    }

def test_survey_screen_integration():
    """测试评估量表界面集成"""
    print("\n=== 测试评估量表界面集成 ===")
    
    try:
        # 检查survey_screen.py是否包含新的方法调用
        with open('screens/survey_screen.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键集成点
        integration_points = [
            'get_mobile_assessments',
            'get_mobile_questionnaires',
            'submit_mobile_assessment',
            'submit_mobile_questionnaire',
            'distribution_manager',
            'get_pending_assessments',
            'get_pending_questionnaires',
            'mark_assessment_completed',
            'mark_questionnaire_completed'
        ]
        
        missing_points = []
        for point in integration_points:
            if point not in content:
                missing_points.append(point)
        
        if missing_points:
            print(f"✗ 评估量表界面缺少以下集成点: {', '.join(missing_points)}")
            return False
        else:
            print("✓ 评估量表界面集成点检查通过")
            return True
        
    except Exception as e:
        print(f"✗ 评估量表界面集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试移动端分发通知集成功能...\n")
    
    test_results = []
    
    # 运行所有测试
    test_results.append(("云端API集成", test_cloud_api_integration()))
    test_results.append(("分发管理器", test_distribution_manager()))
    test_results.append(("推送通知处理器", test_push_notification_handler()))
    test_results.append(("评估量表界面集成", test_survey_screen_integration()))
    
    # 汇总测试结果
    print("\n" + "="*50)
    print("测试结果汇总:")
    print("="*50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！移动端分发通知集成功能正常工作。")
        return True
    else:
        print(f"\n❌ {total - passed} 个测试失败，请检查相关功能。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
