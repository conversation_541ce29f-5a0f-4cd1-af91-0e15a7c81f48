#!/usr/bin/env python3
"""
测试分发通知集成功能
"""

import json
import logging
from datetime import datetime, timedelta

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_distribution_notification():
    """测试分发通知处理"""
    print("=== 测试分发通知处理 ===")
    
    # 导入分发管理器
    from utils.distribution_manager import get_distribution_manager
    distribution_manager = get_distribution_manager()
    
    # 创建测试评估量表分发通知
    assessment_notification = {
        "id": "dist_assessment_test_001",
        "type": "distribution",
        "content_type": "assessment",
        "title": "心理健康评估量表分发",
        "message": "您有一个新的心理健康评估量表需要完成",
        "distribution_data": {
            "distribution_id": 12345,
            "template_id": 1,
            "template_key": "hamilton_depression",
            "assessment_info": {
                "name": "汉密尔顿抑郁量表",
                "name_en": "Hamilton Depression Rating Scale",
                "description": "用于评估抑郁症状严重程度的专业量表",
                "category": "心理健康",
                "assessment_type": "depression",
                "sub_type": "clinical",
                "version": "1.0",
                "max_score": 52,
                "estimated_time": 15
            },
            "questions": [
                {
                    "id": 1,
                    "text": "抑郁情绪（悲伤、绝望、无助、无价值感）",
                    "type": "single_choice",
                    "options": [
                        {"value": 0, "text": "无"},
                        {"value": 1, "text": "轻度"},
                        {"value": 2, "text": "中度"},
                        {"value": 3, "text": "重度"},
                        {"value": 4, "text": "极重度"}
                    ],
                    "required": True
                },
                {
                    "id": 2,
                    "text": "感到悲伤和沮丧",
                    "type": "single_choice",
                    "options": [
                        {"value": 0, "text": "无"},
                        {"value": 1, "text": "轻度"},
                        {"value": 2, "text": "中度"},
                        {"value": 3, "text": "重度"},
                        {"value": 4, "text": "极重度"}
                    ],
                    "required": True
                }
            ],
            "instructions": "请根据您最近一周的感受选择最符合的选项",
            "scoring_method": "sum",
            "result_ranges": [
                {"min": 0, "max": 7, "level": "正常", "description": "无抑郁症状"},
                {"min": 8, "max": 16, "level": "轻度", "description": "轻度抑郁"},
                {"min": 17, "max": 23, "level": "中度", "description": "中度抑郁"},
                {"min": 24, "max": 52, "level": "重度", "description": "重度抑郁"}
            ],
            "due_date": (datetime.now() + timedelta(days=7)).isoformat() + "Z",
            "distributor": {
                "id": 1,
                "name": "张医生",
                "role": "doctor"
            }
        },
        "created_at": datetime.now().isoformat() + "Z",
        "expires_at": (datetime.now() + timedelta(days=7)).isoformat() + "Z",
        "priority": "high",
        "read_status": False
    }
    
    # 处理评估量表分发通知
    success = distribution_manager.process_distribution_notification(assessment_notification)
    if success:
        print("✓ 评估量表分发通知处理成功")
    else:
        print("✗ 评估量表分发通知处理失败")
        return False
    
    # 创建测试问卷分发通知
    questionnaire_notification = {
        "id": "dist_questionnaire_test_001",
        "type": "distribution",
        "content_type": "questionnaire",
        "title": "健康生活方式调查问卷分发",
        "message": "请完成健康生活方式调查问卷",
        "distribution_data": {
            "distribution_id": 67890,
            "questionnaire_id": 2,
            "questionnaire_info": {
                "title": "健康生活方式调查",
                "description": "了解您的日常生活习惯和健康状况",
                "category": "生活方式",
                "questionnaire_type": "survey",
                "assessment_type": "self",
                "estimated_time": 10
            },
            "questions": [
                {
                    "id": 1,
                    "text": "您每天的睡眠时间是多少？",
                    "type": "single_choice",
                    "options": [
                        {"value": "less_than_6", "text": "少于6小时"},
                        {"value": "6_to_8", "text": "6-8小时"},
                        {"value": "more_than_8", "text": "超过8小时"}
                    ],
                    "required": True
                },
                {
                    "id": 2,
                    "text": "请描述您的运动习惯",
                    "type": "text",
                    "required": False,
                    "max_length": 500
                }
            ],
            "instructions": "请根据您的实际情况如实填写",
            "due_date": (datetime.now() + timedelta(days=7)).isoformat() + "Z",
            "distributor": {
                "id": 1,
                "name": "健康管理员",
                "role": "health_manager"
            }
        },
        "created_at": datetime.now().isoformat() + "Z",
        "expires_at": (datetime.now() + timedelta(days=7)).isoformat() + "Z",
        "priority": "medium",
        "read_status": False
    }
    
    # 处理问卷分发通知
    success = distribution_manager.process_distribution_notification(questionnaire_notification)
    if success:
        print("✓ 问卷分发通知处理成功")
    else:
        print("✗ 问卷分发通知处理失败")
        return False
    
    return True

def test_pending_items():
    """测试获取待完成项目"""
    print("\n=== 测试获取待完成项目 ===")
    
    from utils.distribution_manager import get_distribution_manager
    distribution_manager = get_distribution_manager()
    
    # 获取待完成的评估量表
    pending_assessments = distribution_manager.get_pending_assessments("test_user")
    print(f"待完成评估量表数量: {len(pending_assessments)}")
    for assessment in pending_assessments:
        print(f"  - {assessment.get('name')} (ID: {assessment.get('id')})")
    
    # 获取待完成的问卷
    pending_questionnaires = distribution_manager.get_pending_questionnaires("test_user")
    print(f"待完成问卷数量: {len(pending_questionnaires)}")
    for questionnaire in pending_questionnaires:
        print(f"  - {questionnaire.get('title')} (ID: {questionnaire.get('id')})")
    
    return len(pending_assessments) > 0 or len(pending_questionnaires) > 0

def test_cloud_api_methods():
    """测试云端API新方法"""
    print("\n=== 测试云端API新方法 ===")
    
    from utils.cloud_api import get_cloud_api
    cloud_api = get_cloud_api()
    
    # 测试获取移动端评估量表方法是否存在
    if hasattr(cloud_api, 'get_mobile_assessments'):
        print("✓ get_mobile_assessments 方法存在")
    else:
        print("✗ get_mobile_assessments 方法不存在")
    
    # 测试获取移动端问卷方法是否存在
    if hasattr(cloud_api, 'get_mobile_questionnaires'):
        print("✓ get_mobile_questionnaires 方法存在")
    else:
        print("✗ get_mobile_questionnaires 方法不存在")
    
    # 测试提交移动端评估量表方法是否存在
    if hasattr(cloud_api, 'submit_mobile_assessment'):
        print("✓ submit_mobile_assessment 方法存在")
    else:
        print("✗ submit_mobile_assessment 方法不存在")
    
    # 测试提交移动端问卷方法是否存在
    if hasattr(cloud_api, 'submit_mobile_questionnaire'):
        print("✓ submit_mobile_questionnaire 方法存在")
    else:
        print("✗ submit_mobile_questionnaire 方法不存在")
    
    return True

def test_completion_marking():
    """测试完成标记功能"""
    print("\n=== 测试完成标记功能 ===")
    
    from utils.distribution_manager import get_distribution_manager
    distribution_manager = get_distribution_manager()
    
    # 标记评估量表为已完成
    success = distribution_manager.mark_assessment_completed(
        12345, 
        {
            'total_score': 15,
            'answers_count': 2,
            'submitted_at': datetime.now().isoformat()
        }
    )
    
    if success:
        print("✓ 评估量表完成标记成功")
    else:
        print("✗ 评估量表完成标记失败")
    
    # 标记问卷为已完成
    success = distribution_manager.mark_questionnaire_completed(
        67890,
        {
            'answers_count': 2,
            'submitted_at': datetime.now().isoformat()
        }
    )
    
    if success:
        print("✓ 问卷完成标记成功")
    else:
        print("✗ 问卷完成标记失败")
    
    return True

def main():
    """主测试函数"""
    print("开始测试分发通知集成功能...\n")
    
    try:
        # 测试分发通知处理
        if not test_distribution_notification():
            print("分发通知处理测试失败")
            return False
        
        # 测试获取待完成项目
        if not test_pending_items():
            print("获取待完成项目测试失败")
            return False
        
        # 测试云端API新方法
        if not test_cloud_api_methods():
            print("云端API新方法测试失败")
            return False
        
        # 测试完成标记功能
        if not test_completion_marking():
            print("完成标记功能测试失败")
            return False
        
        print("\n=== 所有测试通过 ===")
        print("分发通知集成功能正常工作")
        return True
        
    except Exception as e:
        print(f"\n测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
