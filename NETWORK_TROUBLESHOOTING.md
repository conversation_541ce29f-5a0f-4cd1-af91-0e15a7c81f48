# 移动端网络问题排查指南

## 问题描述

移动端应用在上传文件到云端和下载评估量表时出现网络连接失败，错误信息显示代理连接问题：

```
HTTPConnectionPool(host='127.0.0.1', port=7890): Max retries exceeded with url: http://************:80/api/documents/upload (Caused by ProxyError('Unable to connect to proxy', NewConnectionError...))
```

## 问题原因

1. **代理设置冲突**：系统中存在代理环境变量（如 `HTTP_PROXY=127.0.0.1:7890`），导致网络请求被错误地路由到代理服务器
2. **代理服务器不可用**：代理服务器（127.0.0.1:7890）未运行或不可访问
3. **网络配置问题**：DNS解析或防火墙设置阻止了正常的网络连接

## 解决方案

### 方案一：使用自动修复脚本（推荐）

1. 运行网络修复脚本：
   ```bash
   python fix_network_issues.py
   ```

2. 脚本会自动：
   - 清除所有代理环境变量
   - 测试网络连接
   - 检查DNS解析
   - 清除DNS缓存
   - 生成诊断报告

3. 重启移动端应用

### 方案二：手动修复

#### 步骤1：清除代理环境变量

**Windows (命令提示符):**
```cmd
set HTTP_PROXY=
set HTTPS_PROXY=
set http_proxy=
set https_proxy=
set ALL_PROXY=
set all_proxy=
set SOCKS_PROXY=
set socks_proxy=
```

**Windows (PowerShell):**
```powershell
$env:HTTP_PROXY = $null
$env:HTTPS_PROXY = $null
$env:http_proxy = $null
$env:https_proxy = $null
$env:ALL_PROXY = $null
$env:all_proxy = $null
```

**Linux/macOS:**
```bash
unset HTTP_PROXY
unset HTTPS_PROXY
unset http_proxy
unset https_proxy
unset ALL_PROXY
unset all_proxy
```

#### 步骤2：清除DNS缓存

**Windows:**
```cmd
ipconfig /flushdns
```

**macOS:**
```bash
sudo dscacheutil -flushcache
```

**Linux:**
```bash
sudo systemctl restart systemd-resolved
# 或
sudo service nscd restart
```

#### 步骤3：测试网络连接

使用以下命令测试服务器连接：

```bash
# 测试主服务器
curl -I http://************:80/api/health

# 测试本地服务器
curl -I http://localhost:8006/api/health

# 测试基本网络连接
ping ************
```

### 方案三：配置代理绕过

如果必须使用代理，请配置代理绕过规则：

1. 将以下地址添加到代理绕过列表：
   - `************`
   - `localhost`
   - `127.0.0.1`

2. 或设置 `NO_PROXY` 环境变量：
   ```bash
   export NO_PROXY="************,localhost,127.0.0.1"
   ```

## 验证修复

修复后，请验证以下功能：

1. **文件上传**：尝试上传一个小文件到云端
2. **评估量表下载**：进入评估量表页面，检查是否能正常加载量表列表
3. **网络连接**：检查应用日志，确认没有代理相关错误

## 预防措施

为避免类似问题再次发生：

1. **检查代理软件**：
   - 关闭不必要的代理软件（如某些VPN客户端）
   - 确保代理软件正确配置或完全禁用

2. **环境变量管理**：
   - 定期检查系统环境变量
   - 避免设置全局代理环境变量

3. **网络监控**：
   - 使用网络诊断工具定期检查连接状态
   - 监控应用日志中的网络错误

## 常见错误和解决方法

### 错误1：ProxyError
```
ProxyError('Unable to connect to proxy')
```
**解决方法**：清除代理环境变量，重启应用

### 错误2：ConnectionRefusedError
```
[WinError 10061] 由于目标计算机积极拒绝，无法连接
```
**解决方法**：检查服务器状态，确认服务器地址正确

### 错误3：TimeoutError
```
requests.exceptions.Timeout
```
**解决方法**：检查网络连接，增加超时时间

### 错误4：DNS解析失败
```
socket.gaierror: [Errno 11001] getaddrinfo failed
```
**解决方法**：清除DNS缓存，更换DNS服务器

## 技术支持

如果上述方法都无法解决问题，请：

1. 运行 `fix_network_issues.py` 生成诊断报告
2. 收集应用日志文件
3. 记录具体的错误信息和重现步骤
4. 联系技术支持团队

## 快速验证修复

运行测试脚本验证修复是否有效：

```bash
python test_network_fix.py
```

该脚本会测试：
- 代理清除功能
- 网络请求兼容性
- API模块导入
- 关键组件功能

## 常见问题解答

### Q: 为什么会出现 "trust_env" 参数错误？
A: 这是因为不同版本的 requests 库对 `trust_env` 参数的支持不同。我们的修复已经添加了版本兼容性检查，会自动适配不同版本的 requests。

### Q: 修复后仍然有网络问题怎么办？
A:
1. 重启移动端应用
2. 运行 `python test_network_fix.py` 验证修复
3. 检查防火墙和杀毒软件设置
4. 尝试使用不同的网络环境

### Q: 如何确认代理已完全禁用？
A: 运行测试脚本，或者在命令行中执行：
```bash
echo $HTTP_PROXY $HTTPS_PROXY $http_proxy $https_proxy
```
如果输出为空，说明代理已清除。

## 更新日志

- **2025-01-27**：添加自动代理检测和修复功能
- **2025-01-27**：增强网络请求的代理禁用机制
- **2025-01-27**：创建网络诊断工具和修复脚本
- **2025-01-27**：修复 requests 库版本兼容性问题
- **2025-01-27**：添加网络修复验证测试脚本
