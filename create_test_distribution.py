#!/usr/bin/env python3
"""
创建测试分发通知数据
"""

import sys
import os
from datetime import datetime, timedelta

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def create_test_distribution():
    """创建测试分发通知"""
    print("=== 创建测试分发通知 ===")
    
    try:
        from utils.distribution_manager import get_distribution_manager
        distribution_manager = get_distribution_manager()
        
        # 创建测试评估量表分发通知
        assessment_notification = {
            "id": "test_dist_12345",
            "type": "distribution",
            "content_type": "assessment",
            "title": "汉密尔顿抑郁量表分发",
            "message": "您有一个新的心理健康评估量表需要完成",
            "distribution_data": {
                "distribution_id": 12345,
                "template_id": 1,
                "template_key": "hamilton_depression",
                "assessment_info": {
                    "name": "汉密尔顿抑郁量表",
                    "name_en": "Hamilton Depression Rating Scale",
                    "description": "用于评估抑郁症状严重程度的专业量表",
                    "category": "心理健康",
                    "assessment_type": "depression",
                    "sub_type": "clinical",
                    "version": "1.0",
                    "max_score": 52,
                    "estimated_time": 15
                },
                "questions": [
                    {
                        "id": 1,
                        "text": "抑郁情绪（悲伤、绝望、无助、无价值感）",
                        "type": "single_choice",
                        "options": [
                            {"value": 0, "text": "无", "score": 0},
                            {"value": 1, "text": "轻度", "score": 1},
                            {"value": 2, "text": "中度", "score": 2},
                            {"value": 3, "text": "重度", "score": 3},
                            {"value": 4, "text": "极重度", "score": 4}
                        ],
                        "required": True
                    },
                    {
                        "id": 2,
                        "text": "感到悲伤和沮丧",
                        "type": "single_choice",
                        "options": [
                            {"value": 0, "text": "无", "score": 0},
                            {"value": 1, "text": "轻度", "score": 1},
                            {"value": 2, "text": "中度", "score": 2},
                            {"value": 3, "text": "重度", "score": 3},
                            {"value": 4, "text": "极重度", "score": 4}
                        ],
                        "required": True
                    },
                    {
                        "id": 3,
                        "text": "睡眠障碍（入睡困难、早醒、睡眠不深）",
                        "type": "single_choice",
                        "options": [
                            {"value": 0, "text": "无", "score": 0},
                            {"value": 1, "text": "轻度", "score": 1},
                            {"value": 2, "text": "中度", "score": 2},
                            {"value": 3, "text": "重度", "score": 3},
                            {"value": 4, "text": "极重度", "score": 4}
                        ],
                        "required": True
                    },
                    {
                        "id": 4,
                        "text": "工作和活动能力下降",
                        "type": "single_choice",
                        "options": [
                            {"value": 0, "text": "无", "score": 0},
                            {"value": 1, "text": "轻度", "score": 1},
                            {"value": 2, "text": "中度", "score": 2},
                            {"value": 3, "text": "重度", "score": 3},
                            {"value": 4, "text": "极重度", "score": 4}
                        ],
                        "required": True
                    },
                    {
                        "id": 5,
                        "text": "迟缓（思维和言语迟缓、注意力不集中、活动减少）",
                        "type": "single_choice",
                        "options": [
                            {"value": 0, "text": "无", "score": 0},
                            {"value": 1, "text": "轻度", "score": 1},
                            {"value": 2, "text": "中度", "score": 2},
                            {"value": 3, "text": "重度", "score": 3},
                            {"value": 4, "text": "极重度", "score": 4}
                        ],
                        "required": True
                    }
                ],
                "instructions": "请根据您最近一周的感受选择最符合的选项。每个问题都有0-4分的评分，请仔细阅读每个选项后选择。",
                "scoring_method": "sum",
                "result_ranges": [
                    {"min": 0, "max": 7, "level": "正常", "description": "无抑郁症状"},
                    {"min": 8, "max": 16, "level": "轻度", "description": "轻度抑郁"},
                    {"min": 17, "max": 23, "level": "中度", "description": "中度抑郁"},
                    {"min": 24, "max": 52, "level": "重度", "description": "重度抑郁"}
                ],
                "due_date": (datetime.now() + timedelta(days=7)).isoformat() + "Z",
                "distributor": {
                    "id": 1,
                    "name": "张医生",
                    "role": "doctor",
                    "department": "心理科"
                }
            },
            "created_at": datetime.now().isoformat() + "Z",
            "expires_at": (datetime.now() + timedelta(days=7)).isoformat() + "Z",
            "priority": "high",
            "read_status": False
        }
        
        # 处理分发通知
        success = distribution_manager.process_distribution_notification(assessment_notification)
        
        if success:
            print("✓ 测试评估量表分发通知创建成功")
            
            # 验证数据
            pending_assessments = distribution_manager.get_pending_assessments("SM_006")
            print(f"✓ 当前待完成评估量表数量: {len(pending_assessments)}")
            
            if pending_assessments:
                assessment = pending_assessments[0]
                print(f"✓ 评估量表信息:")
                print(f"  - ID: {assessment.get('id')}")
                print(f"  - 名称: {assessment.get('name')}")
                print(f"  - 描述: {assessment.get('description')}")
                print(f"  - 题目数量: {len(assessment.get('questions', []))}")
                print(f"  - 状态: {assessment.get('status')}")
                print(f"  - 到期时间: {assessment.get('due_date')}")
                
                # 显示题目信息
                questions = assessment.get('questions', [])
                print(f"✓ 题目详情:")
                for i, q in enumerate(questions[:3]):  # 只显示前3题
                    print(f"  题目 {i+1}: {q.get('text')}")
                    print(f"    类型: {q.get('type')}")
                    print(f"    选项数量: {len(q.get('options', []))}")
                    if q.get('options'):
                        print(f"    第一个选项: {q['options'][0]}")
                
                return True
            else:
                print("✗ 没有找到创建的评估量表")
                return False
        else:
            print("✗ 测试评估量表分发通知创建失败")
            return False
            
    except Exception as e:
        print(f"✗ 创建测试分发通知失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_test_questionnaire():
    """创建测试问卷分发通知"""
    print("\n=== 创建测试问卷分发通知 ===")
    
    try:
        from utils.distribution_manager import get_distribution_manager
        distribution_manager = get_distribution_manager()
        
        # 创建测试问卷分发通知
        questionnaire_notification = {
            "id": "test_quest_67890",
            "type": "distribution",
            "content_type": "questionnaire",
            "title": "健康生活方式调查问卷分发",
            "message": "请完成健康生活方式调查问卷",
            "distribution_data": {
                "distribution_id": 67890,
                "questionnaire_id": 2,
                "questionnaire_info": {
                    "title": "健康生活方式调查",
                    "description": "了解您的日常生活习惯和健康状况",
                    "category": "生活方式",
                    "questionnaire_type": "survey",
                    "assessment_type": "self",
                    "estimated_time": 10
                },
                "questions": [
                    {
                        "id": 1,
                        "text": "您每天的睡眠时间是多少？",
                        "type": "single_choice",
                        "options": [
                            {"value": "less_than_6", "text": "少于6小时"},
                            {"value": "6_to_8", "text": "6-8小时"},
                            {"value": "more_than_8", "text": "超过8小时"}
                        ],
                        "required": True
                    },
                    {
                        "id": 2,
                        "text": "您每周运动几次？",
                        "type": "single_choice",
                        "options": [
                            {"value": "never", "text": "从不运动"},
                            {"value": "1_2_times", "text": "1-2次"},
                            {"value": "3_4_times", "text": "3-4次"},
                            {"value": "5_plus_times", "text": "5次以上"}
                        ],
                        "required": True
                    },
                    {
                        "id": 3,
                        "text": "请描述您的饮食习惯",
                        "type": "text",
                        "required": False,
                        "max_length": 500
                    }
                ],
                "instructions": "请根据您的实际情况如实填写",
                "due_date": (datetime.now() + timedelta(days=7)).isoformat() + "Z",
                "distributor": {
                    "id": 2,
                    "name": "健康管理员",
                    "role": "health_manager"
                }
            },
            "created_at": datetime.now().isoformat() + "Z",
            "expires_at": (datetime.now() + timedelta(days=7)).isoformat() + "Z",
            "priority": "medium",
            "read_status": False
        }
        
        # 处理分发通知
        success = distribution_manager.process_distribution_notification(questionnaire_notification)
        
        if success:
            print("✓ 测试问卷分发通知创建成功")
            
            # 验证数据
            pending_questionnaires = distribution_manager.get_pending_questionnaires("SM_006")
            print(f"✓ 当前待完成问卷数量: {len(pending_questionnaires)}")
            
            return True
        else:
            print("✗ 测试问卷分发通知创建失败")
            return False
            
    except Exception as e:
        print(f"✗ 创建测试问卷分发通知失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("开始创建测试分发通知数据...\n")
    
    success1 = create_test_distribution()
    success2 = create_test_questionnaire()
    
    if success1 and success2:
        print("\n🎉 测试分发通知数据创建成功！")
        print("\n现在您可以:")
        print("1. 重新启动移动应用")
        print("2. 进入评估量表页面")
        print("3. 应该能看到 '汉密尔顿抑郁量表' (ID: 12345)")
        print("4. 点击该量表应该能正常打开，不会出现502错误")
        print("5. 问卷页面应该能看到 '健康生活方式调查'")
        print("\n这些数据来自本地分发管理器，不需要网络连接。")
        return True
    else:
        print("\n❌ 测试分发通知数据创建失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
