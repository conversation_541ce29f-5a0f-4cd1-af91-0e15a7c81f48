# 推送通知处理器
import json
import logging
from typing import Dict, Optional, Callable
from datetime import datetime

logger = logging.getLogger(__name__)

class PushNotificationHandler:
    """推送通知处理器，处理来自云端的推送通知"""
    
    def __init__(self):
        """初始化推送通知处理器"""
        self.notification_callbacks = {}
        self.distribution_manager = None
        
    def register_callback(self, notification_type: str, callback: Callable):
        """注册通知类型的回调函数
        
        Args:
            notification_type: 通知类型 (如 'distribution', 'reminder', 'message')
            callback: 回调函数
        """
        if notification_type not in self.notification_callbacks:
            self.notification_callbacks[notification_type] = []
        self.notification_callbacks[notification_type].append(callback)
        logger.info(f"注册通知回调: {notification_type}")
    
    def handle_notification(self, notification_data: Dict) -> bool:
        """处理推送通知
        
        Args:
            notification_data: 推送通知数据
            
        Returns:
            bool: 处理是否成功
        """
        try:
            # 验证通知数据格式
            if not self._validate_notification(notification_data):
                logger.error("推送通知格式无效")
                return False
            
            notification_type = notification_data.get('type')
            logger.info(f"收到推送通知，类型: {notification_type}")
            
            # 根据通知类型进行处理
            if notification_type == 'distribution':
                return self._handle_distribution_notification(notification_data)
            elif notification_type == 'reminder':
                return self._handle_reminder_notification(notification_data)
            elif notification_type == 'message':
                return self._handle_message_notification(notification_data)
            else:
                logger.warning(f"未知的通知类型: {notification_type}")
                return False
                
        except Exception as e:
            logger.error(f"处理推送通知失败: {str(e)}")
            return False
    
    def _validate_notification(self, notification_data: Dict) -> bool:
        """验证推送通知格式"""
        required_fields = ['id', 'type', 'title', 'message', 'created_at']
        return all(field in notification_data for field in required_fields)
    
    def _handle_distribution_notification(self, notification_data: Dict) -> bool:
        """处理分发通知"""
        try:
            # 获取分发管理器
            if self.distribution_manager is None:
                from utils.distribution_manager import get_distribution_manager
                self.distribution_manager = get_distribution_manager()
            
            # 处理分发通知
            success = self.distribution_manager.process_distribution_notification(notification_data)
            
            if success:
                logger.info("分发通知处理成功")
                # 调用注册的回调函数
                self._call_callbacks('distribution', notification_data)
            else:
                logger.error("分发通知处理失败")
            
            return success
            
        except Exception as e:
            logger.error(f"处理分发通知异常: {str(e)}")
            return False
    
    def _handle_reminder_notification(self, notification_data: Dict) -> bool:
        """处理提醒通知"""
        try:
            logger.info(f"处理提醒通知: {notification_data.get('title')}")
            
            # 调用注册的回调函数
            self._call_callbacks('reminder', notification_data)
            
            return True
            
        except Exception as e:
            logger.error(f"处理提醒通知异常: {str(e)}")
            return False
    
    def _handle_message_notification(self, notification_data: Dict) -> bool:
        """处理消息通知"""
        try:
            logger.info(f"处理消息通知: {notification_data.get('title')}")
            
            # 调用注册的回调函数
            self._call_callbacks('message', notification_data)
            
            return True
            
        except Exception as e:
            logger.error(f"处理消息通知异常: {str(e)}")
            return False
    
    def _call_callbacks(self, notification_type: str, notification_data: Dict):
        """调用注册的回调函数"""
        callbacks = self.notification_callbacks.get(notification_type, [])
        for callback in callbacks:
            try:
                callback(notification_data)
            except Exception as e:
                logger.error(f"回调函数执行失败: {str(e)}")

class MockPushNotificationService:
    """模拟推送通知服务，用于测试"""
    
    def __init__(self, handler: PushNotificationHandler):
        """初始化模拟推送通知服务
        
        Args:
            handler: 推送通知处理器
        """
        self.handler = handler
        self.is_connected = False
    
    def connect(self) -> bool:
        """连接到推送通知服务"""
        try:
            logger.info("连接到推送通知服务...")
            self.is_connected = True
            logger.info("推送通知服务连接成功")
            return True
        except Exception as e:
            logger.error(f"连接推送通知服务失败: {str(e)}")
            return False
    
    def disconnect(self):
        """断开推送通知服务连接"""
        self.is_connected = False
        logger.info("推送通知服务连接已断开")
    
    def simulate_distribution_notification(self, content_type: str, distribution_data: Dict) -> bool:
        """模拟分发通知
        
        Args:
            content_type: 内容类型 ('assessment' 或 'questionnaire')
            distribution_data: 分发数据
            
        Returns:
            bool: 模拟是否成功
        """
        if not self.is_connected:
            logger.error("推送通知服务未连接")
            return False
        
        # 构建分发通知
        notification = {
            "id": f"mock_dist_{content_type}_{datetime.now().timestamp()}",
            "type": "distribution",
            "content_type": content_type,
            "title": f"新的{content_type}分发",
            "message": f"您有一个新的{content_type}需要完成",
            "distribution_data": distribution_data,
            "created_at": datetime.now().isoformat() + "Z",
            "priority": "high",
            "read_status": False
        }
        
        # 处理通知
        return self.handler.handle_notification(notification)
    
    def simulate_reminder_notification(self, title: str, message: str) -> bool:
        """模拟提醒通知
        
        Args:
            title: 提醒标题
            message: 提醒消息
            
        Returns:
            bool: 模拟是否成功
        """
        if not self.is_connected:
            logger.error("推送通知服务未连接")
            return False
        
        # 构建提醒通知
        notification = {
            "id": f"mock_reminder_{datetime.now().timestamp()}",
            "type": "reminder",
            "title": title,
            "message": message,
            "created_at": datetime.now().isoformat() + "Z",
            "priority": "medium",
            "read_status": False
        }
        
        # 处理通知
        return self.handler.handle_notification(notification)

# 全局实例
_push_notification_handler = None
_mock_push_service = None

def get_push_notification_handler():
    """获取推送通知处理器实例（单例模式）"""
    global _push_notification_handler
    if _push_notification_handler is None:
        _push_notification_handler = PushNotificationHandler()
    return _push_notification_handler

def get_mock_push_service():
    """获取模拟推送通知服务实例（单例模式）"""
    global _mock_push_service
    if _mock_push_service is None:
        handler = get_push_notification_handler()
        _mock_push_service = MockPushNotificationService(handler)
    return _mock_push_service

def setup_notification_callbacks():
    """设置通知回调函数"""
    handler = get_push_notification_handler()
    
    # 注册分发通知回调
    def on_distribution_notification(notification_data):
        logger.info(f"收到分发通知: {notification_data.get('title')}")
        # 这里可以添加UI更新逻辑，比如刷新评估量表列表
        
    # 注册提醒通知回调
    def on_reminder_notification(notification_data):
        logger.info(f"收到提醒通知: {notification_data.get('title')}")
        # 这里可以添加UI提醒逻辑
        
    # 注册消息通知回调
    def on_message_notification(notification_data):
        logger.info(f"收到消息通知: {notification_data.get('title')}")
        # 这里可以添加消息显示逻辑
    
    handler.register_callback('distribution', on_distribution_notification)
    handler.register_callback('reminder', on_reminder_notification)
    handler.register_callback('message', on_message_notification)
    
    logger.info("通知回调函数设置完成")
