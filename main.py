import os
import json
import logging
import sys
from datetime import datetime
import threading
import time

# 设置Python递归深度限制，防止递归错误
sys.setrecursionlimit(3000)  # 大幅增加递归深度限制，默认值通常为1000

# 设置KivyMD版本环境变量，解决版本警告问题
os.environ['KIVYMD_REPO'] = 'https://github.com/kivymd/KivyMD.git'
os.environ['KIVYMD_BRANCH'] = 'master'

# 禁用代理设置，避免代理连接问题
proxy_vars_to_remove = [
    'HTTP_PROXY', 'HTTPS_PROXY', 'FTP_PROXY',
    'http_proxy', 'https_proxy', 'ftp_proxy',
    'ALL_PROXY', 'all_proxy',
    'SOCKS_PROXY', 'socks_proxy',
    'SOCKS4_PROXY', 'socks4_proxy',
    'SOCKS5_PROXY', 'socks5_proxy'
]

for proxy_var in proxy_vars_to_remove:
    os.environ.pop(proxy_var, None)

# 强制设置NO_PROXY环境变量
os.environ['NO_PROXY'] = '*'
os.environ['no_proxy'] = '*'

# 添加当前目录到路径
import sys
import os.path as osp
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 现在可以导入utils模块
from utils.app_config import LOG_CONFIG, STORAGE_CONFIG

# 导入并初始化代理配置，确保禁用代理
from utils.proxy_config import initialize_proxy_config
initialize_proxy_config()

# 确保必要的目录存在
os.makedirs(STORAGE_CONFIG['DATA_DIR'], exist_ok=True)
os.makedirs(STORAGE_CONFIG['CACHE_DIR'], exist_ok=True)
os.makedirs(STORAGE_CONFIG['QUEUE_DIR'], exist_ok=True)

# 在导入任何Kivy相关模块之前设置基本日志配置
# 这样可以避免Kivy的日志系统与Python标准日志系统之间的冲突
import logging
# 设置更高的默认日志级别，减少DEBUG日志输出
logging.basicConfig(
    level=logging.WARNING,  # 默认使用WARNING级别，减少日志输出
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
# 为主模块设置INFO级别，保留重要信息
logging.getLogger(__name__).setLevel(logging.INFO)

# 创建日志目录（如果不存在）
os.makedirs(LOG_CONFIG['LOG_DIR'], exist_ok=True)

# 保存原始的sys.stderr，以便在出现问题时可以恢复
original_stderr = sys.stderr

# 定义一个简单的异常处理函数，避免复杂逻辑
def simple_exception_handler(exc_type, exc_value, exc_traceback):
    # 忽略KeyboardInterrupt异常
    if issubclass(exc_type, KeyboardInterrupt):
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return

    # 恢复原始stderr（如果丢失）
    if sys.stderr is None:
        sys.stderr = original_stderr

    # 简单地打印异常信息
    print(f"未捕获的异常: {exc_type.__name__}: {exc_value}", file=original_stderr)
    import traceback
    traceback.print_exception(exc_type, exc_value, exc_traceback, file=original_stderr)

    # 如果是递归错误，强制退出程序
    if issubclass(exc_type, RecursionError):
        print("检测到递归错误！应用程序将退出。", file=original_stderr)
        os._exit(1)

# 设置全局异常处理器
sys.excepthook = simple_exception_handler

# 现在导入Kivy日志系统
from kivy.logger import Logger as KivyLogger

# 抑制KivyMD FocusBehavior弃用警告
import warnings
def filter_kivymd_warnings(message, category, filename, lineno, file=None, line=None):
    """过滤KivyMD的FocusBehavior弃用警告"""
    message_str = str(message).lower()
    # 检查是否为FocusBehavior相关的弃用警告
    if ("focusbehavior" in message_str and
        ("deprecated" in message_str or "recommend" in message_str) and
        "statefocusbehavior" in message_str):
        return  # 忽略这个警告
    # 显示其他警告
    warnings.showwarning(message, category, filename, lineno, file, line)

# 设置警告过滤器
warnings.showwarning = filter_kivymd_warnings

# 同时抑制Kivy Logger中的相关警告
original_kivy_warning = KivyLogger.warning
def filtered_kivy_warning(msg):
    """过滤Kivy Logger中的FocusBehavior警告"""
    msg_str = str(msg).lower()
    # 检查是否为FocusBehavior相关的弃用警告
    if ("focusbehavior" in msg_str and
        ("deprecated" in msg_str or "recommend" in msg_str) and
        "statefocusbehavior" in msg_str):
        return  # 忽略这个警告
    original_kivy_warning(msg)

KivyLogger.warning = filtered_kivy_warning

# 额外的日志过滤器，处理标准Python logging模块的警告
class FocusBehaviorFilter(logging.Filter):
    """过滤FocusBehavior弃用警告的日志过滤器"""
    def filter(self, record):
        message = record.getMessage().lower()
        # 过滤FocusBehavior相关的弃用警告
        if ("focusbehavior" in message and
            ("deprecated" in message or "recommend" in message) and
            "statefocusbehavior" in message):
            return False  # 不记录这个日志
        return True  # 记录其他日志

# 为所有相关的logger添加过滤器
focus_filter = FocusBehaviorFilter()
logging.getLogger('kivy').addFilter(focus_filter)
logging.getLogger('kivymd').addFilter(focus_filter)
logging.getLogger().addFilter(focus_filter)  # 根logger

# 导入并应用简化的日志配置
from logging_config import configure_logging
configure_logging()

# 获取日志记录器并记录启动信息
logger = logging.getLogger(__name__)

# 延迟导入内存工具，避免启动时的性能开销
# 循环引用检查将在应用完全启动后异步执行

logger.info("应用程序启动")

from kivy.uix.screenmanager import ScreenManager
from kivy.core.window import Window
from kivy.utils import platform
from kivy.factory import Factory
from kivymd.app import MDApp
from kivy.core.text import LabelBase
import os.path as osp

# 延迟导入UI组件，减少启动时间
# 这些组件将在实际需要时才导入

# 为KivyMD 2.0.1 添加兼容性补丁
import logging
logger = logging.getLogger(__name__)

def apply_kivymd_compatibility_patches():
    """为KivyMD 2.0.1应用兼容性补丁"""
    try:
        # 获取KivyMD版本并记录详细信息
        from kivymd import __version__ as kivymd_version
        logger.info(f"检测到KivyMD版本: {kivymd_version}")

        # 检查是否为2.0.1 dev0版本
        is_dev_version = "dev" in kivymd_version.lower()
        is_2_0_version = kivymd_version.startswith("2.0")

        if is_2_0_version:
            logger.info("检测到KivyMD 2.0系列版本，应用相应补丁")
            if is_dev_version:
                logger.info("检测到开发版本，可能需要特殊处理")
        else:
            logger.info(f"非2.0版本: {kivymd_version}，仍应用通用补丁")

        # 补丁1: 确保MDLabel支持padding_x参数
        patch_mdlabel_padding()

        # 这里可以添加更多针对KivyMD 2.0.1的补丁

        logger.info("KivyMD兼容性补丁应用完成")
    except Exception as e:
        logger.error(f"应用KivyMD兼容性补丁时出错: {e}")
        import traceback
        logger.error(traceback.format_exc())

def patch_mdlabel_padding():
    """为MDLabel添加padding_x参数支持"""
    try:
        # 延迟导入MDLabel，避免启动时的导入开销
        from kivymd.uix.label import MDLabel

        logger.info("为MDLabel添加padding_x参数支持")

        # 保存原始初始化方法
        original_init = MDLabel.__init__

        def patched_init(self, **kwargs):
            """处理初始化时的padding_x参数"""
            # 提取padding_x参数
            padding_x = None
            if 'padding_x' in kwargs:
                padding_x = kwargs.pop('padding_x')

            # 如果指定了padding_x，在kwargs中设置padding属性
            if padding_x is not None:
                if 'padding' in kwargs:
                    current_padding = list(kwargs['padding'])
                    if len(current_padding) == 4:
                        current_padding[0] = padding_x  # 左侧
                        current_padding[2] = padding_x  # 右侧
                        kwargs['padding'] = current_padding
                else:
                    # 没有指定padding，创建新的
                    kwargs['padding'] = [padding_x, 0, padding_x, 0]

            # 调用原始初始化方法
            original_init(self, **kwargs)

        # 应用补丁
        MDLabel.__init__ = patched_init
        logger.info("MDLabel padding_x补丁已应用")
    except ImportError as e:
        logger.warning(f"无法导入MDLabel进行补丁: {e}")
    except Exception as e:
        logger.error(f"应用MDLabel补丁时出错: {e}")

# 应用KivyMD兼容性补丁
apply_kivymd_compatibility_patches()

# 添加工具函数，用于设置水平padding
def set_mdlabel_horizontal_padding(label, value):
    """设置MDLabel的水平padding

    Args:
        label: MDLabel实例
        value: 水平padding值
    """
    if hasattr(label, 'padding'):
        current_padding = list(label.padding) if label.padding else [0, 0, 0, 0]
        if len(current_padding) == 4:
            current_padding[0] = value  # 左侧
            current_padding[2] = value  # 右侧
            label.padding = current_padding
        else:
            label.padding = [value, 0, value, 0]
    else:
        # 在KivyMD 2.0.1中，可能需要直接设置style属性
        try:
            # 尝试使用新的API
            if hasattr(label, 'style') and hasattr(label.style, 'padding'):
                current_padding = list(label.style.padding) if label.style.padding else [0, 0, 0, 0]
                current_padding[0] = value  # 左侧
                current_padding[2] = value  # 右侧
                label.style.padding = current_padding
        except Exception as e:
            logger.error(f"无法设置MDLabel的padding: {e}")

# 延迟导入屏幕类，减少启动时间
# 屏幕将在build()方法中按需导入

# 导入主题配置（这些是必需的）
from theme import AppTheme, AppMetrics, FontStyles

# 延迟导入图标字体设置模块
# 将在应用启动后再注册图标字体

# 添加资源路径解析函数
def resource_path(relative_path):
    """解析相对资源路径为绝对路径"""
    base_path = osp.dirname(osp.abspath(__file__))
    return osp.join(base_path, relative_path)

# 优化的字体注册函数
def register_fonts_optimized():
    """优化的字体注册函数，减少启动时间"""
    try:
        from theme import FontManager
        fonts_dir = resource_path('assets/fonts')

        # 只注册必要的字体，其他字体延迟加载
        essential_fonts = {
            "NotoSans": "NotoSansSC-Regular.ttf",
            "NotoSansMedium": "NotoSansSC-Medium.ttf"
        }

        registered_count = 0
        for font_name, font_file in essential_fonts.items():
            font_path = osp.join(fonts_dir, font_file)
            if osp.exists(font_path) and osp.getsize(font_path) > 0:
                LabelBase.register(name=font_name, fn_regular=font_path)
                registered_count += 1

        # 设置默认字体
        default_font = osp.join(fonts_dir, "NotoSansSC-Regular.ttf")
        if osp.exists(default_font) and osp.getsize(default_font) > 0:
            LabelBase.register(name="Roboto", fn_regular=default_font)
            registered_count += 1

        logger.info(f"快速注册了 {registered_count} 个必要字体")

        # 延迟注册其他字体
        from kivy.clock import Clock
        Clock.schedule_once(lambda dt: register_remaining_fonts(), 1.0)

    except Exception as e:
        logger.error(f"字体注册时出错: {e}")

def register_remaining_fonts():
    """延迟注册剩余字体"""
    try:
        from theme import FontManager
        fonts_dir = resource_path('assets/fonts')

        # 注册剩余字体
        remaining_fonts = {
            "NotoSansLight": "NotoSansSC-Light.ttf",
            "NotoSansSemiBold": "NotoSansSC-SemiBold.ttf",
            "NotoSansBlack": "NotoSansSC-Black.ttf",
            "MSYH": "msyh.ttf"
        }

        registered_count = 0
        for font_name, font_file in remaining_fonts.items():
            font_path = osp.join(fonts_dir, font_file)
            if osp.exists(font_path) and osp.getsize(font_path) > 0:
                LabelBase.register(name=font_name, fn_regular=font_path)
                registered_count += 1

        logger.info(f"延迟注册了 {registered_count} 个字体")

    except Exception as e:
        logger.error(f"延迟字体注册时出错: {e}")

# 使用优化的字体注册
register_fonts_optimized()

class MobileScreenManager(ScreenManager):
    pass

class HealthApp(MDApp):
    """健康管理应用主类"""
    theme = AppTheme
    metrics = AppMetrics
    font_styles = FontStyles
    user_data = {}  # 明确定义用户数据属性，初始为空

    def __init__(self, **kwargs):
        """初始化应用"""
        super().__init__(**kwargs)
        self.title = "健康管理系统"
        self.user_data = {}

        # 确保数据目录存在
        self.data_dir = STORAGE_CONFIG['DATA_DIR']
        os.makedirs(self.data_dir, exist_ok=True)

        # 初始化用户数据文件
        self.user_data_file = os.path.join(self.data_dir, 'user_data.json')
        if not os.path.exists(self.user_data_file):
            with open(self.user_data_file, 'w', encoding='utf-8') as f:
                json.dump({"accounts": []}, f)

        # 初始化通知系统
        self.setup_notification_system()

        # 初始化分发通知系统
        self.setup_distribution_notification_system()

    def setup_notification_system(self):
        """初始化通知系统"""
        from kivy.utils import platform

        # 根据平台配置通知系统
        if platform == 'android':
            # Android平台使用Toast
            try:
                from kivymd.toast import toast
                self.show_notification = toast
            except Exception as e:
                logger.error(f"Android通知系统初始化失败: {e}")
                self.show_notification = self.show_dialog
        else:
            # 其他平台使用MDDialog
            self.show_notification = self.show_dialog

    def show_dialog(self, message, duration=2.0):
        """显示对话框通知

        Args:
            message: 通知消息
            duration: 显示时长（秒）
        """
        try:
            from kivymd.uix.dialog import MDDialog
            from kivymd.uix.button import MDFlatButton
            from kivy.clock import Clock

            def show_message(dt):
                dialog = MDDialog(
                    text=message,
                    buttons=[
                        MDFlatButton(
                            text="确定",
                            theme_text_color="Custom",
                            text_color=(0, 0.5, 1, 1),  # 蓝色
                            on_release=lambda x: dialog.dismiss()
                        ),
                    ],
                )
                dialog.open()

                # 设置自动关闭
                if duration > 0:
                    Clock.schedule_once(lambda dt: dialog.dismiss(), duration)

            Clock.schedule_once(show_message, 0)
        except Exception as e:
            logger.error(f"显示对话框通知失败: {e}")

    def show_info(self, message, duration=2.0):
        """显示信息通知（兼容旧接口）"""
        self.show_notification(message)

    def setup_distribution_notification_system(self):
        """初始化分发通知系统"""
        try:
            # 设置推送通知处理器
            from utils.push_notification_handler import setup_notification_callbacks, get_push_notification_handler
            setup_notification_callbacks()

            # 获取推送通知处理器
            self.push_handler = get_push_notification_handler()

            # 注册应用级别的分发通知回调
            def on_distribution_received(notification_data):
                """当收到分发通知时的回调"""
                content_type = notification_data.get('content_type')
                title = notification_data.get('title', '新的分发通知')

                # 显示通知给用户
                if content_type == 'assessment':
                    message = "您有新的评估量表需要完成"
                elif content_type == 'questionnaire':
                    message = "您有新的问卷需要填写"
                else:
                    message = "您有新的内容需要处理"

                self.show_notification(f"{title}: {message}")

                # 如果当前在评估量表界面，刷新列表
                try:
                    current_screen = self.root.current_screen
                    if hasattr(current_screen, 'name') and current_screen.name == 'survey':
                        if hasattr(current_screen, 'load_assessments'):
                            current_screen.load_assessments()
                        if hasattr(current_screen, 'load_questionnaires'):
                            current_screen.load_questionnaires()
                except Exception as e:
                    logger.error(f"刷新评估量表界面失败: {e}")

            # 注册回调
            self.push_handler.register_callback('distribution', on_distribution_received)

            logger.info("分发通知系统初始化完成")

        except Exception as e:
            logger.error(f"初始化分发通知系统失败: {e}")
            import traceback
            traceback.print_exc()

    def on_start(self):
        """应用启动时调用"""
        # 初始化用户管理器，但不加载当前用户信息
        from utils.user_manager import get_user_manager
        user_manager = get_user_manager()

        # 检查用户数据文件是否存在且非空
        if not os.path.exists(self.user_data_file) or os.path.getsize(self.user_data_file) == 0:
            # 创建一个有效的空数据结构
            with open(self.user_data_file, 'w', encoding='utf-8') as f:
                json.dump({"accounts": []}, f)

        # 启动屏幕懒加载和内存优化
        self.setup_performance_optimizations()

        # 检查是否存在注册队列，如果存在且当前用户已登录，尝试处理队列
        try:
            import threading
            from utils.cloud_api import get_cloud_api

            # 使用我们新的process_register_queue方法处理队列
            def process_register_queue_background():
                try:
                    # 等待5秒，确保应用完全初始化
                    time.sleep(5)

                    # 直接调用实例方法处理注册队列
                    self.process_register_queue()

                except Exception as e:
                    logger.error(f"处理注册队列时出错: {e}")
                    import traceback
                    traceback.print_exc()

            # 在后台线程中处理注册队列
            threading.Thread(target=process_register_queue_background, daemon=True).start()
        except Exception as e:
            logger.error(f"初始化注册队列处理时出错: {e}")

    def setup_performance_optimizations(self):
        """设置性能优化"""
        try:
            # 启动屏幕懒加载
            from utils.screen_loader import get_screen_loader
            screen_loader = get_screen_loader()
            screen_loader.preload_common_screens()

            # 启动内存优化
            from utils.memory_utils import schedule_memory_cleanup, optimize_memory
            from kivy.clock import Clock

            # 执行一次初始内存优化
            Clock.schedule_once(lambda dt: optimize_memory(), 2.0)

            # 启动定期内存清理
            schedule_memory_cleanup()

            logger.info("性能优化设置完成")

        except Exception as e:
            logger.error(f"设置性能优化时出错: {e}")

    def stop(self, *args, **kwargs):
        """明确覆盖stop方法，确保linter识别"""
        print("应用退出")
        return super().stop(*args, **kwargs)

    def build(self):
        # 设置资源目录路径
        self.resource_path = os.path.dirname(os.path.abspath(__file__))
        kivy_resource_path = os.path.join(self.resource_path, 'assets')

        # 如果资源目录不存在，创建它
        if not os.path.exists(kivy_resource_path):
            os.makedirs(kivy_resource_path)

        # 设置窗口大小为6.53英寸屏幕尺寸
        if platform != 'android' and platform != 'ios':
            Window.size = (AppMetrics.SCREEN_WIDTH, AppMetrics.SCREEN_HEIGHT)

        # 设置背景色
        Window.clearcolor = AppTheme.BACKGROUND_COLOR

        # 设置主题颜色
        self.theme_cls.primary_palette = "Blue"
        self.theme_cls.accent_palette = "Amber"
        self.theme_cls.theme_style = "Light"

        # 设置主题字体 - KivyMD 2.x 格式
        # 从KivyMDFontStyles获取字体样式配置
        from kivy.metrics import sp
        # 使用自定义的KivyMDFontStyles类
        from theme import KivyMDFontStyles

        # 设置中文字体样式
        self.theme_cls.font_styles = KivyMDFontStyles.get_font_styles(sp)

        # 初始化Logo管理器，确保只有一个Logo实例
        from widgets.logo_manager import get_logo_manager
        logo_manager = get_logo_manager()

        # 创建屏幕管理器
        sm = MobileScreenManager()
        # 设置过渡动向为从右向左滑动
        sm.transition.direction = 'left'

        # 懒加载屏幕类，减少启动时间
        # 只导入和创建必要的屏幕（登录屏幕）
        from screens.login_screen import LoginScreen
        sm.add_widget(LoginScreen(name="login_screen"))

        # 其他屏幕将在需要时动态加载
        # 这大大减少了应用启动时间

        return sm

    def set_user_data(self, user_data):
        """设置当前用户数据"""
        self.user_data = user_data

    def clear_user_data(self):
        """清除当前用户数据"""
        self.user_data = {}

    def process_register_queue(self, dt=None):
        """处理注册队列"""
        try:
            # 获取CloudAPI实例（使用单例模式）
            from utils.cloud_api import get_cloud_api
            cloud_api = get_cloud_api()

            # 检查是否在降级模式
            if cloud_api.is_in_local_mode():
                logger.debug("云端处于离线模式，跳过注册队列处理")
                return

            # 检查是否登录
            if not cloud_api.is_authenticated():
                logger.debug("用户未登录，跳过注册队列处理")
                return

            # 获取注册队列文件路径
            app_dir = os.path.dirname(os.path.abspath(__file__))
            data_dir = os.path.join(app_dir, 'data')

            # 确保数据目录存在
            if not os.path.exists(data_dir):
                try:
                    os.makedirs(data_dir, exist_ok=True)
                    logger.info(f"创建数据目录: {data_dir}")
                except Exception as e:
                    logger.error(f"创建数据目录失败: {str(e)}")
                    return

            queue_file = os.path.join(data_dir, 'register_queue.json')

            # 检查队列文件是否存在
            if not os.path.exists(queue_file):
                # 队列文件不存在，创建空队列
                try:
                    with open(queue_file, 'w', encoding='utf-8') as f:
                        json.dump([], f)
                    logger.info(f"创建空注册队列文件: {queue_file}")
                    return
                except Exception as e:
                    logger.error(f"创建注册队列文件失败: {str(e)}")
                    return

            # 读取队列
            try:
                with open(queue_file, 'r', encoding='utf-8') as f:
                    queue = json.load(f)
            except json.JSONDecodeError as e:
                logger.error(f"注册队列文件格式错误: {str(e)}")
                # 尝试备份并重新创建队列文件
                try:
                    # 创建备份
                    backup_file = f"{queue_file}.bak.{int(time.time())}"
                    import shutil
                    shutil.copy2(queue_file, backup_file)
                    logger.info(f"已备份损坏的队列文件: {backup_file}")

                    # 重新创建空队列文件
                    with open(queue_file, 'w', encoding='utf-8') as f:
                        json.dump([], f)
                    logger.info("已重新创建空队列文件")
                except Exception as backup_err:
                    logger.error(f"备份/重建队列文件失败: {str(backup_err)}")
                return
            except Exception as e:
                logger.error(f"读取注册队列文件失败: {str(e)}")
                return

            # 检查队列格式
            if not isinstance(queue, list):
                logger.error(f"注册队列格式错误，不是有效的列表")
                try:
                    # 创建备份
                    backup_file = f"{queue_file}.invalid.{int(time.time())}"
                    import shutil
                    shutil.copy2(queue_file, backup_file)
                    logger.info(f"已备份格式错误的队列文件: {backup_file}")

                    # 重新创建空队列文件
                    with open(queue_file, 'w', encoding='utf-8') as f:
                        json.dump([], f)
                    logger.info("已重新创建空队列文件")
                except Exception as backup_err:
                    logger.error(f"备份/重建队列文件失败: {str(backup_err)}")
                return

            # 队列为空则直接返回
            if not queue:
                logger.debug("注册队列为空")
                return

            # 队列有内容，处理最多5条记录
            logger.info(f"发现注册队列，队列大小: {len(queue)}")

            # 处理队列
            processed = 0
            success = 0
            remaining_queue = []

            for user_data in queue[:5]:  # 每次最多处理5条
                try:
                    # 检查是否设置了next_retry时间，如果设置了且未到时间，则跳过此次处理
                    if 'next_retry' in user_data and user_data['next_retry'] > int(time.time()):
                        # 计算剩余等待时间
                        wait_minutes = (user_data['next_retry'] - int(time.time())) / 60
                        logger.info(f"用户 {user_data.get('username')} 的重试时间未到，还需等待 {wait_minutes:.1f} 分钟，跳过此次处理")
                        remaining_queue.append(user_data)
                        continue

                    logger.info(f"从队列中注册用户: {user_data.get('username')}")

                    # 确保有时间戳字段
                    if 'timestamp' not in user_data:
                        user_data['timestamp'] = int(time.time())

                    # 确保用户数据包含必要的字段：username、password_hash和email
                    # 如果只有password而没有password_hash，则计算hash值
                    if 'password' in user_data and 'password_hash' not in user_data:
                        import hashlib
                        password = user_data.get('password', '')
                        user_data['password_hash'] = hashlib.sha256(password.encode()).hexdigest()
                        logger.info(f"已为用户 {user_data.get('username')} 生成密码哈希")
                    elif 'password_hash' not in user_data:
                        # 如果既没有password也没有password_hash，记录错误并跳过此用户
                        logger.error(f"用户 {user_data.get('username')} 缺少密码信息，无法注册")
                        remaining_queue.append(user_data)
                        continue

                    # 确保password_hash不为空
                    if not user_data.get('password_hash'):
                        logger.error(f"用户 {user_data.get('username')} 的password_hash为空，无法注册")
                        remaining_queue.append(user_data)
                        continue

                    # 确保有email字段
                    if 'email' not in user_data and 'phone' in user_data:
                        # 如果没有email但有phone，用phone构建一个临时email
                        user_data['email'] = f"{user_data['phone']}@example.com"
                        logger.info(f"已为用户 {user_data.get('username')} 生成临时邮箱：{user_data['email']}")

                    # 添加时间戳
                    user_data['timestamp'] = int(time.time())

                    # 提交注册请求
                    result = cloud_api.register_user(user_data)
                    processed += 1

                    if result:
                        # 注册成功
                        success += 1
                        logger.info(f"队列注册成功: {user_data.get('username')}")
                    else:
                        # 注册失败
                        error = cloud_api.last_error
                        logger.error(f"队列注册失败: {user_data.get('username')}, 错误: {error}")

                        # 检查是否是用户名或身份证已存在的错误
                        if error and ("已存在" in error or "already exists" in error.lower()):
                            # 已存在的用户不需要重试
                            logger.info(f"用户已存在，从队列中移除: {user_data.get('username')}")
                        else:
                            # 检查result是否包含错误信息（新的错误处理机制）
                            if isinstance(result, dict) and not result.get('success', False):
                                error_code = result.get('error_code', 0)
                                next_retry = result.get('next_retry', 0)

                                # 如果返回了next_retry时间，使用它
                                if next_retry > 0:
                                    user_data['next_retry'] = next_retry
                                    wait_minutes = (next_retry - int(time.time())) / 60
                                    logger.info(f"用户 {user_data.get('username')} 将在 {wait_minutes:.1f} 分钟后重试，错误码: {error_code}")
                                else:
                                    # 添加递增的重试延迟（兼容旧逻辑）
                                    retry_count = user_data.get('retry_count', 0) + 1
                                    user_data['retry_count'] = retry_count
                                    # 指数退避策略
                                    wait_time = min(2 ** retry_count, 30) * 60  # 转换为秒
                                    user_data['next_retry'] = int(time.time()) + wait_time
                                    logger.info(f"用户 {user_data.get('username')} 将在 {wait_time/60:.1f} 分钟后重试，这是第 {retry_count} 次重试")

                                # 保留在队列中
                                remaining_queue.append(user_data)
                            # 兼容旧的错误处理逻辑
                            elif error and "502" in error:
                                logger.warning(f"检测到502网关错误，将用户 {user_data.get('username')} 保留在队列中稍后重试")
                                # 添加递增的重试延迟
                                retry_count = user_data.get('retry_count', 0) + 1
                                user_data['retry_count'] = retry_count
                                # 指数退避策略
                                wait_time = min(2 ** retry_count, 30) * 60  # 转换为秒
                                user_data['next_retry'] = int(time.time()) + wait_time
                                logger.info(f"用户 {user_data.get('username')} 将在 {wait_time/60:.1f} 分钟后重试，这是第 {retry_count} 次重试")
                                # 保留在队列中
                                remaining_queue.append(user_data)
                            else:
                                # 其他错误，也保留在队列中
                                remaining_queue.append(user_data)
                except Exception as e:
                    logger.error(f"处理注册队列项异常: {str(e)}")
                    # 出错时保留到队列中
                    remaining_queue.append(user_data)

            # 保存剩余队列
            remaining_queue.extend(queue[5:])  # 添加未处理的项
            with open(queue_file, 'w', encoding='utf-8') as f:
                json.dump(remaining_queue, f, ensure_ascii=False, indent=2)

            if processed > 0:
                logger.info(f"注册队列处理完成: {success}/{processed} 成功, 剩余 {len(remaining_queue)} 项")

        except Exception as e:
            logger.error(f"处理注册队列时出错: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())

if __name__ == "__main__":
    # 启动性能监控
    from utils.performance_monitor import get_startup_timer, schedule_performance_logging
    startup_timer = get_startup_timer()
    startup_timer.start_phase("应用初始化")

    # 延迟注册图标字体，减少启动时间
    startup_timer.start_phase("图标字体注册")
    try:
        from icon_font_setup import register_icon_font
        register_icon_font()
    except ImportError as e:
        logger.warning(f"无法导入图标字体设置: {e}")
    except Exception as e:
        logger.error(f"注册图标字体时出错: {e}")
    startup_timer.end_phase("图标字体注册")

    # 启动应用
    startup_timer.start_phase("应用启动")
    app = HealthApp()
    startup_timer.end_phase("应用启动")

    # 启动性能日志记录
    schedule_performance_logging()

    # 记录启动完成
    startup_timer.end_phase("应用初始化")

    # 运行应用
    app.run()

