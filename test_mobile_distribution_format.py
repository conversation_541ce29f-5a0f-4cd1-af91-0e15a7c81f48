#!/usr/bin/env python3
"""
测试移动端分发格式兼容性
"""

import sys
import os
import json

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def test_assessment_id_extraction():
    """测试评估量表ID提取逻辑"""
    print("=== 测试评估量表ID提取逻辑 ===")
    
    # 模拟云端API返回的数据格式（按mobile_distribution_format.md规范）
    test_cases = [
        {
            "name": "标准分发格式 - 有template字段",
            "data": {
                "id": 123,
                "distribution_id": 456,
                "name": "汉密尔顿抑郁量表",
                "assessment_type": "depression",
                "status": "pending",
                "template": {
                    "id": 1,
                    "name": "汉密尔顿抑郁量表",
                    "description": "用于评估抑郁症状严重程度",
                    "questions": [
                        {
                            "id": 1,
                            "question_id": "q1",
                            "question_text": "您的心情如何？",
                            "question_type": "single_choice",
                            "options": [
                                {"value": 0, "text": "很好"},
                                {"value": 1, "text": "一般"},
                                {"value": 2, "text": "较差"}
                            ]
                        }
                    ]
                }
            },
            "expected_id": 123,
            "expected_name": "汉密尔顿抑郁量表",
            "expected_questions": 1
        },
        {
            "name": "简化格式 - 无template字段",
            "data": {
                "id": 789,
                "name": "焦虑自评量表",
                "description": "焦虑症状评估",
                "questions": [
                    {
                        "id": 1,
                        "text": "您感到紧张吗？",
                        "type": "single_choice",
                        "options": [
                            {"value": 1, "text": "从不"},
                            {"value": 2, "text": "有时"},
                            {"value": 3, "text": "经常"}
                        ]
                    }
                ]
            },
            "expected_id": 789,
            "expected_name": "焦虑自评量表",
            "expected_questions": 1
        },
        {
            "name": "只有distribution_id",
            "data": {
                "distribution_id": 999,
                "name": "压力评估量表",
                "questions": []
            },
            "expected_id": 999,
            "expected_name": "压力评估量表",
            "expected_questions": 0
        }
    ]
    
    def extract_assessment_id(assessment):
        """模拟修复后的ID提取逻辑"""
        return assessment.get('id') or assessment.get('distribution_id')
    
    def extract_assessment_data(assessment):
        """模拟修复后的数据提取逻辑"""
        if 'template' in assessment:
            # 使用template中的数据
            template_data = assessment['template']
            name = template_data.get('name') or assessment.get('name') or '未命名量表'
            description = template_data.get('description') or assessment.get('description') or '无描述'
            questions = template_data.get('questions', [])
        else:
            # 直接使用assessment中的数据
            name = assessment.get('name') or assessment.get('title') or '未命名量表'
            description = assessment.get('description') or assessment.get('notes') or '无描述'
            questions = assessment.get('questions', [])
        
        return name, description, questions
    
    passed = 0
    total = len(test_cases)
    
    for test_case in test_cases:
        data = test_case["data"]
        expected_id = test_case["expected_id"]
        expected_name = test_case["expected_name"]
        expected_questions = test_case["expected_questions"]
        
        # 测试ID提取
        actual_id = extract_assessment_id(data)
        
        # 测试数据提取
        actual_name, actual_description, actual_questions = extract_assessment_data(data)
        actual_question_count = len(actual_questions)
        
        # 验证结果
        id_correct = actual_id == expected_id
        name_correct = actual_name == expected_name
        questions_correct = actual_question_count == expected_questions
        
        if id_correct and name_correct and questions_correct:
            print(f"✓ {test_case['name']}")
            print(f"  ID: {actual_id}, 名称: {actual_name}, 题目数: {actual_question_count}")
            passed += 1
        else:
            print(f"✗ {test_case['name']}")
            if not id_correct:
                print(f"  ID错误: 期望={expected_id}, 实际={actual_id}")
            if not name_correct:
                print(f"  名称错误: 期望={expected_name}, 实际={actual_name}")
            if not questions_correct:
                print(f"  题目数错误: 期望={expected_questions}, 实际={actual_question_count}")
    
    print(f"\n测试结果: {passed}/{total} 个测试通过")
    return passed == total

def test_api_response_format():
    """测试API响应格式处理"""
    print("\n=== 测试API响应格式处理 ===")
    
    # 模拟云端API可能返回的响应格式
    test_responses = [
        {
            "name": "标准成功响应",
            "response": {
                "status": "success",
                "data": [
                    {
                        "id": 123,
                        "distribution_id": 456,
                        "name": "汉密尔顿抑郁量表",
                        "template": {
                            "name": "汉密尔顿抑郁量表",
                            "questions": [{"id": 1, "text": "测试题目"}]
                        }
                    }
                ]
            },
            "expected_count": 1,
            "expected_first_id": 123
        },
        {
            "name": "直接返回列表",
            "response": [
                {
                    "id": 789,
                    "name": "焦虑自评量表",
                    "questions": [{"id": 1, "text": "测试题目"}]
                }
            ],
            "expected_count": 1,
            "expected_first_id": 789
        },
        {
            "name": "空响应",
            "response": {
                "status": "success",
                "data": []
            },
            "expected_count": 0,
            "expected_first_id": None
        }
    ]
    
    def process_api_response(response):
        """模拟修复后的API响应处理逻辑"""
        if isinstance(response, list):
            # 过滤掉非字典项
            filtered_list = []
            for item in response:
                if isinstance(item, dict):
                    filtered_list.append(item)
            return filtered_list
        elif isinstance(response, dict):
            if response.get('status') == 'success':
                data = response.get('data', [])
                if isinstance(data, list):
                    # 过滤掉非字典项
                    filtered_list = []
                    for item in data:
                        if isinstance(item, dict):
                            filtered_list.append(item)
                    return filtered_list
        return []
    
    def extract_assessment_id(assessment):
        """ID提取逻辑"""
        return assessment.get('id') or assessment.get('distribution_id')
    
    passed = 0
    total = len(test_responses)
    
    for test_case in test_responses:
        response = test_case["response"]
        expected_count = test_case["expected_count"]
        expected_first_id = test_case["expected_first_id"]
        
        processed_data = process_api_response(response)
        actual_count = len(processed_data)
        
        if actual_count == expected_count:
            if expected_count > 0:
                actual_first_id = extract_assessment_id(processed_data[0])
                if actual_first_id == expected_first_id:
                    print(f"✓ {test_case['name']}: 数量={actual_count}, 第一个ID={actual_first_id}")
                    passed += 1
                else:
                    print(f"✗ {test_case['name']}: ID错误 (期望={expected_first_id}, 实际={actual_first_id})")
            else:
                print(f"✓ {test_case['name']}: 空响应处理正确")
                passed += 1
        else:
            print(f"✗ {test_case['name']}: 数量错误 (期望={expected_count}, 实际={actual_count})")
    
    print(f"\n测试结果: {passed}/{total} 个测试通过")
    return passed == total

def test_question_format_compatibility():
    """测试题目格式兼容性"""
    print("\n=== 测试题目格式兼容性 ===")
    
    # 模拟不同的题目格式
    question_formats = [
        {
            "name": "标准格式",
            "question": {
                "id": 1,
                "question_id": "q1",
                "question_text": "您感觉如何？",
                "question_type": "single_choice",
                "options": [
                    {"value": 1, "text": "很好"},
                    {"value": 2, "text": "一般"}
                ]
            },
            "expected_text": "您感觉如何？",
            "expected_options": 2
        },
        {
            "name": "简化格式",
            "question": {
                "id": 1,
                "text": "您的心情如何？",
                "type": "single_choice",
                "options": [
                    {"value": 1, "text": "好"},
                    {"value": 2, "text": "不好"}
                ]
            },
            "expected_text": "您的心情如何？",
            "expected_options": 2
        }
    ]
    
    def extract_question_data(question):
        """模拟题目数据提取逻辑"""
        # 提取题目文本
        text = (question.get('question_text') or 
                question.get('text') or 
                question.get('title') or '')
        
        # 提取选项
        options = question.get('options', [])
        
        return text, options
    
    passed = 0
    total = len(question_formats)
    
    for test_case in question_formats:
        question = test_case["question"]
        expected_text = test_case["expected_text"]
        expected_options = test_case["expected_options"]
        
        actual_text, actual_options = extract_question_data(question)
        actual_option_count = len(actual_options)
        
        if actual_text == expected_text and actual_option_count == expected_options:
            print(f"✓ {test_case['name']}: 文本='{actual_text}', 选项数={actual_option_count}")
            passed += 1
        else:
            print(f"✗ {test_case['name']}")
            if actual_text != expected_text:
                print(f"  文本错误: 期望='{expected_text}', 实际='{actual_text}'")
            if actual_option_count != expected_options:
                print(f"  选项数错误: 期望={expected_options}, 实际={actual_option_count}")
    
    print(f"\n测试结果: {passed}/{total} 个测试通过")
    return passed == total

def main():
    """主测试函数"""
    print("开始测试移动端分发格式兼容性...\n")
    
    test1_passed = test_assessment_id_extraction()
    test2_passed = test_api_response_format()
    test3_passed = test_question_format_compatibility()
    
    if test1_passed and test2_passed and test3_passed:
        print("\n🎉 所有测试通过！")
        print("\n修复说明:")
        print("1. 支持mobile_distribution_format.md规范的数据格式")
        print("2. 正确处理template字段")
        print("3. 支持多种ID字段名（id优先，然后distribution_id）")
        print("4. 兼容不同的题目和选项格式")
        print("5. 过滤掉非字典类型的数据项")
        print("\n现在移动端应该能正确处理云端分发的量表数据。")
        return True
    else:
        print("\n❌ 部分测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
