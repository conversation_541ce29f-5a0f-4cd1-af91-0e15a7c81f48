#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
网络修复测试脚本
用于验证网络问题修复是否有效
"""

import os
import sys
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_proxy_clearing():
    """测试代理清除功能"""
    print("=" * 50)
    print("测试代理清除功能")
    print("=" * 50)
    
    # 设置一些测试代理变量
    test_proxies = {
        'HTTP_PROXY': 'http://127.0.0.1:7890',
        'HTTPS_PROXY': 'http://127.0.0.1:7890',
        'http_proxy': 'http://127.0.0.1:7890',
        'https_proxy': 'http://127.0.0.1:7890'
    }
    
    print("设置测试代理变量...")
    for key, value in test_proxies.items():
        os.environ[key] = value
        print(f"  {key} = {value}")
    
    # 导入并运行代理清除
    try:
        from utils.proxy_config import disable_proxy_globally, check_proxy_status
        
        print("\n清除前的代理状态:")
        status = check_proxy_status()
        print(f"  检测到 {status['proxy_count']} 个代理变量")
        for var, value in status['active_proxies'].items():
            print(f"    {var} = {value}")
        
        print("\n正在清除代理设置...")
        disable_proxy_globally()
        
        print("\n清除后的代理状态:")
        status = check_proxy_status()
        print(f"  检测到 {status['proxy_count']} 个代理变量")
        
        if status['proxy_count'] == 0:
            print("  ✓ 代理清除成功")
            return True
        else:
            print("  ✗ 代理清除失败")
            return False
            
    except Exception as e:
        print(f"  ✗ 代理清除测试失败: {e}")
        return False

def test_network_request():
    """测试网络请求功能"""
    print("\n" + "=" * 50)
    print("测试网络请求功能")
    print("=" * 50)
    
    try:
        import requests
        import inspect
        
        # 检查requests版本信息
        print(f"requests版本: {requests.__version__}")
        
        # 检查是否支持trust_env参数
        sig = inspect.signature(requests.get)
        supports_trust_env = 'trust_env' in sig.parameters
        print(f"支持trust_env参数: {supports_trust_env}")
        
        # 测试网络请求
        test_urls = [
            'https://www.baidu.com',
            'http://8.138.188.26:80'
        ]
        
        for url in test_urls:
            try:
                print(f"\n测试连接: {url}")
                
                # 构建请求参数
                request_kwargs = {
                    'timeout': 5,
                    'proxies': {
                        'http': None,
                        'https': None,
                        'ftp': None,
                        'socks4': None,
                        'socks5': None
                    },
                    'verify': False
                }
                
                # 如果支持trust_env，添加该参数
                if supports_trust_env:
                    request_kwargs['trust_env'] = False
                
                response = requests.get(url, **request_kwargs)
                print(f"  ✓ 连接成功 (状态码: {response.status_code})")
                
            except Exception as e:
                print(f"  ✗ 连接失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"  ✗ 网络请求测试失败: {e}")
        return False

def test_cloud_api_import():
    """测试云端API导入"""
    print("\n" + "=" * 50)
    print("测试云端API导入")
    print("=" * 50)
    
    try:
        from utils.cloud_api import CloudAPI
        print("  ✓ CloudAPI导入成功")
        
        # 测试创建实例
        api = CloudAPI()
        print("  ✓ CloudAPI实例创建成功")
        
        return True
        
    except Exception as e:
        print(f"  ✗ CloudAPI测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_survey_screen_import():
    """测试评估量表页面导入"""
    print("\n" + "=" * 50)
    print("测试评估量表页面导入")
    print("=" * 50)
    
    try:
        # 这个测试可能会因为Kivy依赖而失败，但我们可以尝试
        from screens.survey_screen import SurveyScreen
        print("  ✓ SurveyScreen导入成功")
        return True
        
    except Exception as e:
        print(f"  ✗ SurveyScreen测试失败: {e}")
        # 这是预期的，因为可能缺少Kivy环境
        return False

def main():
    """主测试函数"""
    print("网络修复验证测试")
    print("=" * 60)
    
    results = {}
    
    # 测试代理清除
    results['proxy_clearing'] = test_proxy_clearing()
    
    # 测试网络请求
    results['network_request'] = test_network_request()
    
    # 测试API导入
    results['cloud_api_import'] = test_cloud_api_import()
    
    # 测试页面导入（可选）
    results['survey_screen_import'] = test_survey_screen_import()
    
    # 显示结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = 0
    
    for test_name, result in results.items():
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name:20} : {status}")
        if result:
            passed += 1
        total += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！网络修复应该已经生效。")
    elif passed >= total - 1:  # 允许survey_screen测试失败
        print("✅ 关键测试通过！网络修复应该已经生效。")
    else:
        print("⚠️  部分测试失败，可能需要进一步检查。")
    
    return passed >= total - 1

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n建议：重启移动端应用以使更改完全生效。")
        else:
            print("\n建议：检查错误信息并运行 fix_network_issues.py 进行修复。")
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    input("\n按回车键退出...")
