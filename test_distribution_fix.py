#!/usr/bin/env python3
"""
测试分发通知修复
"""

import sys
import os
from datetime import datetime, timedelta

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def create_test_distribution_notification():
    """创建测试分发通知"""
    return {
        "id": "test_dist_12345",
        "type": "distribution",
        "content_type": "assessment",
        "title": "汉密尔顿抑郁量表分发",
        "message": "您有一个新的心理健康评估量表需要完成",
        "distribution_data": {
            "distribution_id": 12345,
            "template_id": 1,
            "template_key": "hamilton_depression",
            "assessment_info": {
                "name": "汉密尔顿抑郁量表",
                "name_en": "Hamilton Depression Rating Scale",
                "description": "用于评估抑郁症状严重程度的专业量表",
                "category": "心理健康",
                "assessment_type": "depression",
                "sub_type": "clinical",
                "version": "1.0",
                "max_score": 52,
                "estimated_time": 15
            },
            "questions": [
                {
                    "id": 1,
                    "text": "抑郁情绪（悲伤、绝望、无助、无价值感）",
                    "type": "single_choice",
                    "options": [
                        {"value": 0, "text": "无", "score": 0},
                        {"value": 1, "text": "轻度", "score": 1},
                        {"value": 2, "text": "中度", "score": 2},
                        {"value": 3, "text": "重度", "score": 3},
                        {"value": 4, "text": "极重度", "score": 4}
                    ],
                    "required": True
                },
                {
                    "id": 2,
                    "text": "感到悲伤和沮丧",
                    "type": "single_choice",
                    "options": [
                        {"value": 0, "text": "无", "score": 0},
                        {"value": 1, "text": "轻度", "score": 1},
                        {"value": 2, "text": "中度", "score": 2},
                        {"value": 3, "text": "重度", "score": 3},
                        {"value": 4, "text": "极重度", "score": 4}
                    ],
                    "required": True
                },
                {
                    "id": 3,
                    "text": "睡眠障碍",
                    "type": "single_choice",
                    "options": [
                        {"value": 0, "text": "无", "score": 0},
                        {"value": 1, "text": "轻度", "score": 1},
                        {"value": 2, "text": "中度", "score": 2},
                        {"value": 3, "text": "重度", "score": 3},
                        {"value": 4, "text": "极重度", "score": 4}
                    ],
                    "required": True
                }
            ],
            "instructions": "请根据您最近一周的感受选择最符合的选项",
            "scoring_method": "sum",
            "result_ranges": [
                {"min": 0, "max": 7, "level": "正常", "description": "无抑郁症状"},
                {"min": 8, "max": 16, "level": "轻度", "description": "轻度抑郁"},
                {"min": 17, "max": 23, "level": "中度", "description": "中度抑郁"},
                {"min": 24, "max": 52, "level": "重度", "description": "重度抑郁"}
            ],
            "due_date": (datetime.now() + timedelta(days=7)).isoformat() + "Z",
            "distributor": {
                "id": 1,
                "name": "张医生",
                "role": "doctor"
            }
        },
        "created_at": datetime.now().isoformat() + "Z",
        "expires_at": (datetime.now() + timedelta(days=7)).isoformat() + "Z",
        "priority": "high",
        "read_status": False
    }

def test_distribution_processing():
    """测试分发通知处理"""
    print("=== 测试分发通知处理 ===")
    
    try:
        # 导入分发管理器
        from utils.distribution_manager import get_distribution_manager
        distribution_manager = get_distribution_manager()
        
        # 创建测试通知
        notification = create_test_distribution_notification()
        
        # 处理分发通知
        success = distribution_manager.process_distribution_notification(notification)
        
        if success:
            print("✓ 分发通知处理成功")
            
            # 检查是否能获取到分发的评估量表
            pending_assessments = distribution_manager.get_pending_assessments("SM_006")
            print(f"✓ 获取到 {len(pending_assessments)} 个待完成评估量表")
            
            if pending_assessments:
                assessment = pending_assessments[0]
                print(f"✓ 评估量表详情:")
                print(f"  - ID: {assessment.get('id')}")
                print(f"  - 名称: {assessment.get('name')}")
                print(f"  - 题目数量: {len(assessment.get('questions', []))}")
                print(f"  - 状态: {assessment.get('status')}")
                
                # 测试题目格式
                questions = assessment.get('questions', [])
                if questions:
                    first_question = questions[0]
                    print(f"  - 第一题: {first_question.get('text')}")
                    print(f"  - 选项数量: {len(first_question.get('options', []))}")
                
                return True
            else:
                print("✗ 没有获取到评估量表")
                return False
        else:
            print("✗ 分发通知处理失败")
            return False
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_assessment_detail_retrieval():
    """测试评估量表详情获取"""
    print("\n=== 测试评估量表详情获取 ===")
    
    try:
        from utils.distribution_manager import get_distribution_manager
        distribution_manager = get_distribution_manager()
        
        # 获取待完成的评估量表
        pending_assessments = distribution_manager.get_pending_assessments("SM_006")
        
        if not pending_assessments:
            print("✗ 没有待完成的评估量表")
            return False
        
        assessment = pending_assessments[0]
        assessment_id = assessment.get('id')
        
        print(f"✓ 找到评估量表 ID: {assessment_id}")
        
        # 模拟从分发管理器获取详情的过程
        assessment_detail = None
        for a in pending_assessments:
            if str(a.get('id')) == str(assessment_id):
                assessment_detail = a
                break
        
        if assessment_detail:
            print("✓ 成功从分发管理器获取评估量表详情")
            print(f"  - 名称: {assessment_detail.get('name')}")
            print(f"  - 描述: {assessment_detail.get('description')}")
            print(f"  - 题目数量: {len(assessment_detail.get('questions', []))}")
            
            # 检查题目格式
            questions = assessment_detail.get('questions', [])
            if questions:
                print("✓ 题目格式检查:")
                for i, q in enumerate(questions[:2]):  # 只检查前两题
                    print(f"  题目 {i+1}:")
                    print(f"    - 文本: {q.get('text')}")
                    print(f"    - 类型: {q.get('type')}")
                    print(f"    - 选项数量: {len(q.get('options', []))}")
                    
                    options = q.get('options', [])
                    if options:
                        print(f"    - 第一个选项: {options[0]}")
            
            return True
        else:
            print("✗ 无法从分发管理器获取评估量表详情")
            return False
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_completion_marking():
    """测试完成标记"""
    print("\n=== 测试完成标记 ===")
    
    try:
        from utils.distribution_manager import get_distribution_manager
        distribution_manager = get_distribution_manager()
        
        # 标记评估量表为已完成
        success = distribution_manager.mark_assessment_completed(
            12345,
            {
                'total_score': 15,
                'answers_count': 3,
                'submitted_at': datetime.now().isoformat()
            }
        )
        
        if success:
            print("✓ 评估量表完成标记成功")
            
            # 检查状态是否更新
            pending_assessments = distribution_manager.get_pending_assessments("SM_006")
            print(f"✓ 标记完成后，待完成评估量表数量: {len(pending_assessments)}")
            
            return True
        else:
            print("✗ 评估量表完成标记失败")
            return False
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试分发通知修复...\n")
    
    tests = [
        ("分发通知处理", test_distribution_processing),
        ("评估量表详情获取", test_assessment_detail_retrieval),
        ("完成标记", test_completion_marking),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            if result:
                passed += 1
                print(f"✓ {test_name} 测试通过\n")
            else:
                print(f"✗ {test_name} 测试失败\n")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}\n")
    
    print(f"总结: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！")
        print("分发通知功能已修复，现在应该可以正常工作了。")
        print("\n修复说明:")
        print("1. 评估量表详情现在优先从分发管理器获取")
        print("2. 只有当量表不在分发列表中时才从云端API获取")
        print("3. 分发的量表包含完整的题目和选项数据")
        return True
    else:
        print(f"\n❌ {total - passed} 个测试失败。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
