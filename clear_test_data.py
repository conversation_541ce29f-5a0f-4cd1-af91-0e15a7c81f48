#!/usr/bin/env python3
"""
清除测试分发数据，让移动端重新从云端获取真实数据
"""

import sys
import os
import json

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def clear_distribution_data():
    """清除本地分发数据"""
    print("=== 清除本地分发数据 ===")
    
    try:
        # 清除分发管理器的数据
        data_dir = os.path.join(current_dir, 'data')
        distribution_file = os.path.join(data_dir, 'distribution_notifications.json')
        
        if os.path.exists(distribution_file):
            # 备份原文件
            backup_file = distribution_file + '.backup'
            if os.path.exists(backup_file):
                os.remove(backup_file)
            os.rename(distribution_file, backup_file)
            print(f"✓ 已备份原分发数据到: {backup_file}")
            
            # 创建空的分发数据文件
            empty_data = {
                "notifications": {},
                "assessments": {},
                "questionnaires": {},
                "last_updated": None
            }
            
            with open(distribution_file, 'w', encoding='utf-8') as f:
                json.dump(empty_data, f, ensure_ascii=False, indent=2)
            
            print(f"✓ 已清除分发数据文件: {distribution_file}")
        else:
            print("✓ 分发数据文件不存在，无需清除")
        
        return True
        
    except Exception as e:
        print(f"✗ 清除分发数据失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def clear_cache_data():
    """清除其他缓存数据"""
    print("\n=== 清除缓存数据 ===")
    
    try:
        data_dir = os.path.join(current_dir, 'data')
        
        # 清除可能的缓存文件
        cache_files = [
            'assessment_cache.json',
            'questionnaire_cache.json',
            'api_cache.json'
        ]
        
        cleared_count = 0
        for cache_file in cache_files:
            cache_path = os.path.join(data_dir, cache_file)
            if os.path.exists(cache_path):
                os.remove(cache_path)
                print(f"✓ 已清除缓存文件: {cache_file}")
                cleared_count += 1
        
        if cleared_count == 0:
            print("✓ 没有找到需要清除的缓存文件")
        else:
            print(f"✓ 共清除了 {cleared_count} 个缓存文件")
        
        return True
        
    except Exception as e:
        print(f"✗ 清除缓存数据失败: {e}")
        return False

def modify_assessment_loading_logic():
    """修改评估量表加载逻辑，优先从云端获取"""
    print("\n=== 修改评估量表加载逻辑 ===")
    
    try:
        # 这里我们不直接修改代码文件，而是提供指导
        print("请手动修改 screens/survey_screen.py 中的 _fetch_assessments_in_background 方法:")
        print("1. 注释掉本地分发数据的优先检查")
        print("2. 直接从云端API获取数据")
        print("3. 只有在云端获取失败时才使用本地数据作为备用")
        
        return True
        
    except Exception as e:
        print(f"✗ 修改加载逻辑失败: {e}")
        return False

def main():
    """主函数"""
    print("开始清除测试数据，让移动端重新从云端获取真实数据...\n")
    
    success1 = clear_distribution_data()
    success2 = clear_cache_data()
    success3 = modify_assessment_loading_logic()
    
    if success1 and success2:
        print("\n🎉 测试数据清除成功！")
        print("\n现在请:")
        print("1. 重新启动移动应用")
        print("2. 进入评估量表页面")
        print("3. 应用将从云端获取真实的分发数据")
        print("4. 不再显示本地测试数据")
        
        print("\n如果仍然有问题，请检查:")
        print("- 网络连接是否正常")
        print("- 云端API是否返回正确的数据格式")
        print("- 认证信息是否有效")
        
        return True
    else:
        print("\n❌ 清除测试数据失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
