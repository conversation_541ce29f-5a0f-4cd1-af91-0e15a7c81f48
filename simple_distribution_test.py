#!/usr/bin/env python3
"""
简化的分发通知功能测试
"""

import sys
import os

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def test_basic_imports():
    """测试基本导入"""
    print("=== 测试基本导入 ===")
    
    try:
        # 测试分发管理器导入
        from utils.distribution_manager import get_distribution_manager
        print("✓ 分发管理器导入成功")
        
        # 测试推送通知处理器导入
        from utils.push_notification_handler import get_push_notification_handler
        print("✓ 推送通知处理器导入成功")
        
        # 测试云端API导入
        from utils.cloud_api import get_cloud_api
        print("✓ 云端API导入成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_distribution_manager_basic():
    """测试分发管理器基本功能"""
    print("\n=== 测试分发管理器基本功能 ===")
    
    try:
        from utils.distribution_manager import get_distribution_manager
        
        # 获取分发管理器实例
        manager = get_distribution_manager()
        print("✓ 分发管理器实例创建成功")
        
        # 测试获取待完成项目（应该返回空列表）
        assessments = manager.get_pending_assessments()
        questionnaires = manager.get_pending_questionnaires()
        
        print(f"✓ 获取待完成评估量表: {len(assessments)} 个")
        print(f"✓ 获取待完成问卷: {len(questionnaires)} 个")
        
        return True
        
    except Exception as e:
        print(f"✗ 分发管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cloud_api_methods():
    """测试云端API新方法"""
    print("\n=== 测试云端API新方法 ===")
    
    try:
        from utils.cloud_api import get_cloud_api
        
        # 获取云端API实例
        api = get_cloud_api()
        print("✓ 云端API实例创建成功")
        
        # 检查新方法是否存在
        methods = [
            'get_mobile_assessments',
            'get_mobile_questionnaires',
            'submit_mobile_assessment',
            'submit_mobile_questionnaire'
        ]
        
        for method in methods:
            if hasattr(api, method):
                print(f"✓ {method} 方法存在")
            else:
                print(f"✗ {method} 方法不存在")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ 云端API测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_push_notification_handler():
    """测试推送通知处理器"""
    print("\n=== 测试推送通知处理器 ===")
    
    try:
        from utils.push_notification_handler import get_push_notification_handler
        
        # 获取推送通知处理器实例
        handler = get_push_notification_handler()
        print("✓ 推送通知处理器实例创建成功")
        
        # 测试注册回调
        def test_callback(data):
            print(f"  回调被调用: {data.get('title', 'Unknown')}")
        
        handler.register_callback('test', test_callback)
        print("✓ 回调注册成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 推送通知处理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_structure():
    """测试文件结构"""
    print("\n=== 测试文件结构 ===")
    
    required_files = [
        'utils/distribution_manager.py',
        'utils/push_notification_handler.py',
        'utils/cloud_api.py',
        'screens/survey_screen.py',
        'main.py'
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path} 存在")
        else:
            print(f"✗ {file_path} 不存在")
            missing_files.append(file_path)
    
    return len(missing_files) == 0

def main():
    """主测试函数"""
    print("开始简化的分发通知功能测试...\n")
    
    tests = [
        ("文件结构", test_file_structure),
        ("基本导入", test_basic_imports),
        ("分发管理器基本功能", test_distribution_manager_basic),
        ("云端API新方法", test_cloud_api_methods),
        ("推送通知处理器", test_push_notification_handler),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            if result:
                passed += 1
                print(f"✓ {test_name} 测试通过")
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
    
    print(f"\n总结: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("\n🎉 所有基本测试通过！")
        print("移动端分发通知集成功能的基本组件已正确安装。")
        return True
    else:
        print(f"\n❌ {total - passed} 个测试失败。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
