# 移动端分发通知集成功能

本文档描述了根据 `docs/mobile_distribution_format.md` 指引对移动端脚本进行的修改，以满足移动端与云端的正常通讯。

## 修改概述

### 1. 云端API客户端增强 (`utils/cloud_api.py`)

添加了新的移动端API方法以支持分发通知格式：

#### 新增方法：
- `get_mobile_assessments(custom_id=None)` - 获取移动端分发的评估量表
- `get_mobile_questionnaires(custom_id=None)` - 获取移动端分发的问卷
- `submit_mobile_assessment(assessment_id, answers, custom_id=None)` - 提交移动端评估量表结果
- `submit_mobile_questionnaire(questionnaire_id, answers, custom_id=None)` - 提交移动端问卷结果

#### 特性：
- 支持新的API端点格式 (`/api/mobile/assessments`, `/api/mobile/questionnaires`)
- 使用 `Authorization: Bearer {jwt_token}` 认证
- 使用 `X-User-ID: {custom_id}` 用户标识
- 符合移动端分发格式规范

### 2. 分发通知管理器 (`utils/distribution_manager.py`)

新增的分发通知管理器，负责处理评估量表和问卷的分发通知：

#### 主要功能：
- **通知处理**: 处理符合 `mobile_distribution_format.md` 格式的分发通知
- **本地缓存**: 管理分发数据的本地存储
- **状态管理**: 跟踪评估量表和问卷的完成状态
- **过期检查**: 自动检查和标记过期的分发项目

#### 核心方法：
- `process_distribution_notification(notification)` - 处理分发通知
- `get_pending_assessments(user_id)` - 获取待完成的评估量表
- `get_pending_questionnaires(user_id)` - 获取待完成的问卷
- `mark_assessment_completed(assessment_id, completion_data)` - 标记评估量表为已完成
- `mark_questionnaire_completed(questionnaire_id, completion_data)` - 标记问卷为已完成

### 3. 推送通知处理器 (`utils/push_notification_handler.py`)

新增的推送通知处理器，用于接收和处理来自云端的推送通知：

#### 主要功能：
- **通知路由**: 根据通知类型路由到相应的处理器
- **回调管理**: 支持注册和调用通知回调函数
- **模拟服务**: 提供模拟推送通知服务用于测试

#### 支持的通知类型：
- `distribution` - 分发通知
- `reminder` - 提醒通知
- `message` - 消息通知

### 4. 评估量表界面增强 (`screens/survey_screen.py`)

修改了评估量表界面以支持新的分发通知机制：

#### 主要修改：
- **API调用更新**: 使用新的移动端API方法
- **分发数据集成**: 优先显示分发的评估量表和问卷
- **提交格式**: 支持新的提交数据格式
- **完成标记**: 提交成功后自动标记为已完成

#### 工作流程：
1. 首先从分发管理器获取本地缓存的分发项目
2. 如果没有本地缓存，从云端API获取
3. 提交时使用新的移动端API端点
4. 提交成功后标记为已完成并刷新列表

### 5. 主应用集成 (`main.py`)

在主应用中集成了分发通知系统：

#### 新增功能：
- **通知系统初始化**: 设置推送通知处理器和回调
- **自动刷新**: 收到分发通知时自动刷新相关界面
- **用户通知**: 显示分发通知给用户

## 数据格式支持

### 分发通知格式
支持 `docs/mobile_distribution_format.md` 中定义的完整分发通知格式：

```json
{
  "id": "distribution_id",
  "type": "distribution",
  "content_type": "assessment|questionnaire",
  "title": "通知标题",
  "message": "通知消息",
  "distribution_data": {
    "distribution_id": 12345,
    "template_id": 1,
    "assessment_info": {...},
    "questions": [...],
    "instructions": "填写说明",
    "due_date": "2024-01-01T00:00:00Z",
    "distributor": {...}
  },
  "created_at": "2024-01-01T00:00:00Z",
  "priority": "high|medium|low",
  "read_status": false
}
```

### 提交格式
支持新的移动端提交格式：

```json
{
  "answers": [
    {
      "question_id": 1,
      "answer": "选择的答案",
      "score": 2
    }
  ]
}
```

## 测试

### 测试脚本
提供了完整的测试脚本：

1. **`test_distribution_integration.py`** - 基础分发通知功能测试
2. **`test_mobile_distribution_integration.py`** - 完整集成功能测试

### 运行测试
```bash
# 基础功能测试
python test_distribution_integration.py

# 完整集成测试
python test_mobile_distribution_integration.py
```

### 测试覆盖
- 云端API新方法验证
- 分发通知处理
- 推送通知系统
- 评估量表界面集成
- 数据格式兼容性

## 兼容性

### 向后兼容
- 保持与现有API的兼容性
- 如果新的移动端API方法不可用，自动回退到旧的API
- 支持现有的评估量表和问卷数据格式

### 数据迁移
- 新的分发数据与现有数据格式兼容
- 自动处理字段映射和格式转换
- 保持现有用户数据完整性

## 部署注意事项

### 服务器端要求
- 确保服务器支持新的移动端API端点
- 配置正确的认证机制
- 实现推送通知服务

### 客户端配置
- 确保网络连接正常
- 配置正确的服务器地址
- 启用推送通知权限

## 故障排除

### 常见问题
1. **API调用失败**: 检查网络连接和服务器配置
2. **分发通知未收到**: 检查推送通知服务配置
3. **数据格式错误**: 验证分发通知格式是否符合规范
4. **提交失败**: 检查认证信息和数据格式

### 日志调试
- 启用详细日志记录
- 检查相关模块的日志输出
- 使用测试脚本验证功能

## 总结

本次修改实现了完整的移动端分发通知集成功能，支持：

✅ 新的移动端API端点  
✅ 分发通知处理机制  
✅ 推送通知系统  
✅ 本地数据缓存和状态管理  
✅ 用户界面集成  
✅ 向后兼容性  
✅ 完整的测试覆盖  

移动端现在可以正常接收、处理和响应来自云端的分发通知，实现了与云端的完整通讯功能。
