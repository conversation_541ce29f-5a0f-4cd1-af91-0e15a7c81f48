#!/usr/bin/env python3
"""
测试评估量表ID修复
"""

import sys
import os

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def test_id_extraction():
    """测试ID提取逻辑"""
    print("=== 测试ID提取逻辑 ===")
    
    # 模拟不同的评估量表数据格式
    test_cases = [
        {
            "name": "标准格式 - 有id字段",
            "data": {
                "id": 123,
                "name": "测试量表1",
                "description": "标准格式"
            },
            "expected_id": 123
        },
        {
            "name": "assessment_id字段",
            "data": {
                "assessment_id": 456,
                "name": "测试量表2",
                "description": "使用assessment_id"
            },
            "expected_id": 456
        },
        {
            "name": "template_id字段",
            "data": {
                "template_id": 789,
                "name": "测试量表3",
                "description": "使用template_id"
            },
            "expected_id": 789
        },
        {
            "name": "distribution_id字段",
            "data": {
                "distribution_id": 999,
                "name": "测试量表4",
                "description": "使用distribution_id"
            },
            "expected_id": 999
        },
        {
            "name": "多个ID字段 - 优先级测试",
            "data": {
                "id": 111,
                "assessment_id": 222,
                "template_id": 333,
                "distribution_id": 444,
                "name": "测试量表5",
                "description": "多个ID字段"
            },
            "expected_id": 111  # id字段优先级最高
        },
        {
            "name": "无ID字段",
            "data": {
                "name": "测试量表6",
                "description": "没有任何ID字段"
            },
            "expected_id": None
        }
    ]
    
    # 测试ID提取逻辑
    def extract_assessment_id(assessment):
        """模拟修复后的ID提取逻辑"""
        return (assessment.get('id') or 
                assessment.get('assessment_id') or 
                assessment.get('template_id') or
                assessment.get('distribution_id'))
    
    passed = 0
    total = len(test_cases)
    
    for test_case in test_cases:
        data = test_case["data"]
        expected = test_case["expected_id"]
        actual = extract_assessment_id(data)
        
        if actual == expected:
            print(f"✓ {test_case['name']}: 期望={expected}, 实际={actual}")
            passed += 1
        else:
            print(f"✗ {test_case['name']}: 期望={expected}, 实际={actual}")
    
    print(f"\n测试结果: {passed}/{total} 个测试通过")
    return passed == total

def test_cloud_api_data_format():
    """测试云端API数据格式处理"""
    print("\n=== 测试云端API数据格式处理 ===")
    
    # 模拟云端API可能返回的数据格式
    test_responses = [
        {
            "name": "标准成功响应",
            "response": {
                "status": "success",
                "data": [
                    {
                        "assessment_id": 12345,
                        "name": "汉密尔顿抑郁量表",
                        "description": "专业抑郁评估量表"
                    }
                ]
            },
            "expected_count": 1,
            "expected_id": 12345
        },
        {
            "name": "直接返回列表",
            "response": [
                {
                    "id": 67890,
                    "name": "焦虑自评量表",
                    "description": "焦虑症状评估"
                }
            ],
            "expected_count": 1,
            "expected_id": 67890
        },
        {
            "name": "包含字符串的混合数据",
            "response": {
                "status": "success",
                "data": [
                    {
                        "template_id": 11111,
                        "name": "有效量表",
                        "description": "这是有效数据"
                    },
                    "这是一个字符串，应该被过滤掉",
                    {
                        "distribution_id": 22222,
                        "name": "另一个有效量表",
                        "description": "这也是有效数据"
                    }
                ]
            },
            "expected_count": 2,
            "expected_ids": [11111, 22222]
        }
    ]
    
    def process_api_response(response):
        """模拟修复后的API响应处理逻辑"""
        if isinstance(response, list):
            # 过滤掉非字典项
            filtered_list = []
            for item in response:
                if isinstance(item, dict):
                    filtered_list.append(item)
            return filtered_list
        elif isinstance(response, dict):
            if response.get('status') == 'success':
                data = response.get('data', [])
                if isinstance(data, list):
                    # 过滤掉非字典项
                    filtered_list = []
                    for item in data:
                        if isinstance(item, dict):
                            filtered_list.append(item)
                    return filtered_list
        return []
    
    def extract_assessment_id(assessment):
        """ID提取逻辑"""
        return (assessment.get('id') or 
                assessment.get('assessment_id') or 
                assessment.get('template_id') or
                assessment.get('distribution_id'))
    
    passed = 0
    total = len(test_responses)
    
    for test_case in test_responses:
        response = test_case["response"]
        expected_count = test_case["expected_count"]
        
        processed_data = process_api_response(response)
        actual_count = len(processed_data)
        
        if actual_count == expected_count:
            print(f"✓ {test_case['name']}: 数据数量正确 ({actual_count})")
            
            # 检查ID提取
            if "expected_id" in test_case:
                if processed_data:
                    actual_id = extract_assessment_id(processed_data[0])
                    if actual_id == test_case["expected_id"]:
                        print(f"  ✓ ID提取正确: {actual_id}")
                        passed += 1
                    else:
                        print(f"  ✗ ID提取错误: 期望={test_case['expected_id']}, 实际={actual_id}")
                else:
                    print(f"  ✗ 没有数据可提取ID")
            elif "expected_ids" in test_case:
                actual_ids = [extract_assessment_id(item) for item in processed_data]
                if actual_ids == test_case["expected_ids"]:
                    print(f"  ✓ 多个ID提取正确: {actual_ids}")
                    passed += 1
                else:
                    print(f"  ✗ 多个ID提取错误: 期望={test_case['expected_ids']}, 实际={actual_ids}")
            else:
                passed += 1
        else:
            print(f"✗ {test_case['name']}: 数据数量错误 (期望={expected_count}, 实际={actual_count})")
    
    print(f"\n测试结果: {passed}/{total} 个测试通过")
    return passed == total

def main():
    """主测试函数"""
    print("开始测试评估量表ID修复...\n")
    
    test1_passed = test_id_extraction()
    test2_passed = test_cloud_api_data_format()
    
    if test1_passed and test2_passed:
        print("\n🎉 所有测试通过！")
        print("\n修复说明:")
        print("1. 支持多种ID字段名: id, assessment_id, template_id, distribution_id")
        print("2. 按优先级顺序提取ID")
        print("3. 过滤掉非字典类型的数据项")
        print("4. 处理多种API响应格式")
        print("\n现在移动端应该能正确获取和处理云端分发的量表数据。")
        return True
    else:
        print("\n❌ 部分测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
